/**
 * @file jy901s.c
 * @brief JY901S九轴姿态传感器STM32驱动库实现
 * @version 1.0
 * @date 2025-07-16
 * <AUTHOR>
 */

#include "jy901s_driver.h"

/* 私有函数声明 */
static uint8_t JY901S_CalculateChecksum(uint8_t *data, uint8_t len);
static int8_t JY901S_ProcessPacket(JY901S_Handle_t *handle);
static void JY901S_ConvertAccData(JY901S_Handle_t *handle, uint8_t *data);
static void JY901S_ConvertGyroData(JY901S_Handle_t *handle, uint8_t *data);
static void JY901S_ConvertAngleData(JY901S_Handle_t *handle, uint8_t *data);
static void JY901S_ResetParser(JY901S_Handle_t *handle);
static void JY901S_InitDataStructure(JY901S_Handle_t *handle);
static void JY901S_ProcessCalibration(JY901S_Handle_t *handle);

/**
 * @brief 初始化JY901S传感器
 * @param handle JY901S句柄指针
 * @param huart UART句柄指针
 * @retval 0: 成功, -1: 失败
 */
int8_t JY901S_Create(JY901S_Handle_t *handle, UART_HandleTypeDef *huart)
{
    // 参数检查
    if (handle == NULL || huart == NULL) {
        return -1;
    }

    // 清零句柄结构
    memset(handle, 0, sizeof(JY901S_Handle_t));

    // 绑定UART句柄
    handle->huart = huart;

    // 初始化数据结构
    JY901S_InitDataStructure(handle);

    // 设置默认配置
    handle->data_filter_mask = 0x07;  // 默认启用加速度、陀螺仪、角度数据
    handle->auto_calibrate = 1;       // 默认启用自动校准

    // 重置解析器
    JY901S_ResetParser(handle);

    // 启动UART接收
    if (JY901S_StartReceive(handle) != 0) {
        return -1;
    }

    // 标记初始化完成
    handle->initialized = 1;

    return 0;
}

/**
 * @brief 初始化数据结构
 * @param handle JY901S句柄指针
 */
static void JY901S_InitDataStructure(JY901S_Handle_t *handle)
{
    // 初始化传感器数据
    handle->data.acc_x = 0.0f;
    handle->data.acc_y = 0.0f;
    handle->data.acc_z = 0.0f;
    handle->data.gyro_x = 0.0f;
    handle->data.gyro_y = 0.0f;
    handle->data.gyro_z = 0.0f;
    handle->data.angle_x = 0.0f;
    handle->data.angle_y = 0.0f;
    handle->data.angle_z = 0.0f;
    handle->data.temperature = 0.0f;
    handle->data.acc_valid = 0;
    handle->data.gyro_valid = 0;
    handle->data.angle_valid = 0;
    handle->data.timestamp = 0;

    // 初始化校准数据
    handle->calibration.acc_offset_x = 0.0f;
    handle->calibration.acc_offset_y = 0.0f;
    handle->calibration.acc_offset_z = 0.0f;
    handle->calibration.angle_offset_x = 0.0f;
    handle->calibration.angle_offset_y = 0.0f;
    handle->calibration.angle_offset_z = 0.0f;
    handle->calibration.calibrated = 0;
    handle->calibration.sample_count = 0;

    // 初始化状态
    handle->data_ready = 0;
    handle->last_update_time = HAL_GetTick();
    handle->packet_count = 0;
    handle->error_count = 0;
    handle->checksum_errors = 0;
}

/**
 * @brief 启动UART接收中断
 * @param handle JY901S句柄指针
 * @retval 0: 成功, -1: 失败
 */
int8_t JY901S_StartReceive(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->huart == NULL) {
        return -1;
    }

    // 启动单字节中断接收
    HAL_StatusTypeDef status = HAL_UART_Receive_IT(handle->huart, &handle->rx_byte, 1);

    if (status != HAL_OK) {
        handle->error_count++;
        return -1;
    }

    return 0;
}

/**
 * @brief 计算数据包校验和
 * @param data 数据指针
 * @param len 数据长度
 * @retval 校验和值
 */
static uint8_t JY901S_CalculateChecksum(uint8_t *data, uint8_t len)
{
    uint8_t sum = 0;
    for (uint8_t i = 0; i < len; i++) {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief 重置解析器状态
 * @param handle JY901S句柄指针
 */
static void JY901S_ResetParser(JY901S_Handle_t *handle)
{
    handle->parse_state = JY901S_STATE_HEADER;
    handle->packet_index = 0;
    handle->packet_type = 0;
}

/**
 * @brief 解析接收到的字节数据（在UART中断中调用）
 * @param handle JY901S句柄指针
 * @param byte 接收到的字节
 * @retval 0: 继续接收, 1: 数据包解析完成, -1: 解析错误
 */
int8_t JY901S_ParseByte(JY901S_Handle_t *handle, uint8_t byte)
{
    if (handle == NULL) {
        return -1;
    }
    
    switch (handle->parse_state) {
        case JY901S_STATE_HEADER:
            // 检测帧头 0x55
            if (byte == JY901S_FRAME_HEADER) {
                handle->packet_buffer[0] = byte;
                handle->packet_index = 1;
                handle->parse_state = JY901S_STATE_TYPE;
            }
            break;
            
        case JY901S_STATE_TYPE:
            // 获取数据类型
            handle->packet_buffer[1] = byte;
            handle->packet_type = byte;
            handle->packet_index = 2;
            
            // 检查是否为需要处理的数据类型
            if (byte == JY901S_TYPE_ACC || byte == JY901S_TYPE_GYRO || byte == JY901S_TYPE_ANGLE) {
                handle->parse_state = JY901S_STATE_DATA;
            } else {
                // 不需要的数据类型，跳过整个数据包
                handle->parse_state = JY901S_STATE_DATA;
            }
            break;
            
        case JY901S_STATE_DATA:
            // 接收数据字节
            handle->packet_buffer[handle->packet_index] = byte;
            handle->packet_index++;
            
            // 检查是否接收完所有数据（8字节数据 + 1字节校验和）
            if (handle->packet_index >= JY901S_PACKET_SIZE) {
                handle->parse_state = JY901S_STATE_CHECKSUM;
                
                // 立即处理校验和
                uint8_t calculated_checksum = JY901S_CalculateChecksum(handle->packet_buffer, JY901S_PACKET_SIZE - 1);
                uint8_t received_checksum = handle->packet_buffer[JY901S_PACKET_SIZE - 1];
                
                if (calculated_checksum == received_checksum) {
                    // 校验成功，处理数据包
                    int8_t result = JY901S_ProcessPacket(handle);
                    JY901S_ResetParser(handle);
                    
                    if (result == 0) {
                        handle->packet_count++;
                        return 1; // 数据包解析完成
                    } else {
                        handle->error_count++;
                        return -1; // 处理错误
                    }
                } else {
                    // 校验失败
                    handle->checksum_errors++;
                    handle->error_count++;
                    JY901S_ResetParser(handle);
                    return -1;
                }
            }
            break;
            
        case JY901S_STATE_CHECKSUM:
            // 这个状态在上面的DATA状态中已经处理了
            JY901S_ResetParser(handle);
            break;
            
        default:
            JY901S_ResetParser(handle);
            break;
    }
    
    return 0; // 继续接收
}

/**
 * @brief 处理完整的数据包
 * @param handle JY901S句柄指针
 * @retval 0: 成功, -1: 失败
 */
static int8_t JY901S_ProcessPacket(JY901S_Handle_t *handle)
{
    uint8_t *data = &handle->packet_buffer[2]; // 跳过帧头和类型字节
    
    // 根据数据类型处理数据
    switch (handle->packet_type) {
        case JY901S_TYPE_ACC:
            // 检查数据过滤掩码
            if (handle->data_filter_mask & 0x01) {
                JY901S_ConvertAccData(handle, data);
                handle->data.acc_valid = 1;
            }
            break;
            
        case JY901S_TYPE_GYRO:
            // 检查数据过滤掩码
            if (handle->data_filter_mask & 0x02) {
                JY901S_ConvertGyroData(handle, data);
                handle->data.gyro_valid = 1;
            }
            break;
            
        case JY901S_TYPE_ANGLE:
            // 检查数据过滤掩码
            if (handle->data_filter_mask & 0x04) {
                JY901S_ConvertAngleData(handle, data);
                handle->data.angle_valid = 1;
            }
            break;
            
        default:
            // 未知数据类型
            return -1;
    }
    
    // 更新时间戳和数据就绪标志
    handle->data.timestamp = HAL_GetTick();
    handle->last_update_time = handle->data.timestamp;
    handle->data_ready = 1;

    // 如果启用自动校准且校准未完成，处理校准数据
    if (handle->auto_calibrate && !handle->calibration.calibrated) {
        JY901S_ProcessCalibration(handle);
    }

    return 0;
}

/**
 * @brief 转换加速度数据
 * @param handle JY901S句柄指针
 * @param data 原始数据指针
 */
static void JY901S_ConvertAccData(JY901S_Handle_t *handle, uint8_t *data)
{
    int16_t acc_x_raw = (int16_t)((data[1] << 8) | data[0]);
    int16_t acc_y_raw = (int16_t)((data[3] << 8) | data[2]);
    int16_t acc_z_raw = (int16_t)((data[5] << 8) | data[4]);
    int16_t temp_raw = (int16_t)((data[7] << 8) | data[6]);
    
    // 转换为浮点数并应用校准偏移
    handle->data.acc_x = (float)acc_x_raw * JY901S_ACC_SCALE - handle->calibration.acc_offset_x;
    handle->data.acc_y = (float)acc_y_raw * JY901S_ACC_SCALE - handle->calibration.acc_offset_y;
    handle->data.acc_z = (float)acc_z_raw * JY901S_ACC_SCALE - handle->calibration.acc_offset_z;
    handle->data.temperature = (float)temp_raw * JY901S_TEMP_SCALE;
}

/**
 * @brief 转换陀螺仪数据
 * @param handle JY901S句柄指针
 * @param data 原始数据指针
 */
static void JY901S_ConvertGyroData(JY901S_Handle_t *handle, uint8_t *data)
{
    int16_t gyro_x_raw = (int16_t)((data[1] << 8) | data[0]);
    int16_t gyro_y_raw = (int16_t)((data[3] << 8) | data[2]);
    int16_t gyro_z_raw = (int16_t)((data[5] << 8) | data[4]);
    int16_t temp_raw = (int16_t)((data[7] << 8) | data[6]);
    
    // 转换为浮点数
    handle->data.gyro_x = (float)gyro_x_raw * JY901S_GYRO_SCALE;
    handle->data.gyro_y = (float)gyro_y_raw * JY901S_GYRO_SCALE;
    handle->data.gyro_z = (float)gyro_z_raw * JY901S_GYRO_SCALE;
    handle->data.temperature = (float)temp_raw * JY901S_TEMP_SCALE;
}

/**
 * @brief 转换角度数据
 * @param handle JY901S句柄指针
 * @param data 原始数据指针
 */
static void JY901S_ConvertAngleData(JY901S_Handle_t *handle, uint8_t *data)
{
    int16_t angle_x_raw = (int16_t)((data[1] << 8) | data[0]);
    int16_t angle_y_raw = (int16_t)((data[3] << 8) | data[2]);
    int16_t angle_z_raw = (int16_t)((data[5] << 8) | data[4]);
    int16_t temp_raw = (int16_t)((data[7] << 8) | data[6]);
    
    // 转换为浮点数并应用校准偏移
    handle->data.angle_x = (float)angle_x_raw * JY901S_ANGLE_SCALE - handle->calibration.angle_offset_x;
    handle->data.angle_y = (float)angle_y_raw * JY901S_ANGLE_SCALE - handle->calibration.angle_offset_y;
    handle->data.angle_z = (float)angle_z_raw * JY901S_ANGLE_SCALE - handle->calibration.angle_offset_z;
    handle->data.temperature = (float)temp_raw * JY901S_TEMP_SCALE;
}

/**
 * @brief 获取最新的传感器数据
 * @param handle JY901S句柄指针
 * @retval 传感器数据指针
 */
JY901S_Data_t* JY901S_GetData(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return NULL;
    }
    return &handle->data;
}

/**
 * @brief 检查是否有新数据就绪
 * @param handle JY901S句柄指针
 * @retval 1: 有新数据, 0: 无新数据
 */
uint8_t JY901S_IsDataReady(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }
    return handle->data_ready;
}

/**
 * @brief 清除数据就绪标志
 * @param handle JY901S句柄指针
 */
void JY901S_ClearDataReady(JY901S_Handle_t *handle)
{
    if (handle != NULL) {
        handle->data_ready = 0;
    }
}

/**
 * @brief 设置数据类型过滤掩码
 * @param handle JY901S句柄指针
 * @param mask 过滤掩码 (位0:加速度, 位1:陀螺仪, 位2:角度)
 */
void JY901S_SetDataFilter(JY901S_Handle_t *handle, uint8_t mask)
{
    if (handle != NULL) {
        handle->data_filter_mask = mask;
    }
}

/**
 * @brief 获取统计信息
 * @param handle JY901S句柄指针
 * @param packet_count 数据包计数指针
 * @param error_count 错误计数指针
 */
void JY901S_GetStatistics(JY901S_Handle_t *handle, uint32_t *packet_count, uint32_t *error_count)
{
    if (handle != NULL) {
        if (packet_count != NULL) {
            *packet_count = handle->packet_count;
        }
        if (error_count != NULL) {
            *error_count = handle->error_count;
        }
    }
}

/**
 * @brief 检查数据是否超时
 * @param handle JY901S句柄指针
 * @retval 1: 数据超时, 0: 数据正常
 */
uint8_t JY901S_IsDataTimeout(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 1;
    }

    uint32_t current_time = HAL_GetTick();
    return (current_time - handle->last_update_time) > JY901S_DATA_TIMEOUT_MS;
}

/**
 * @brief UART接收完成回调函数（用户需要在stm32f1xx_it.c中调用）
 * @param handle JY901S句柄指针
 * @note 这个函数应该在HAL_UART_RxCpltCallback中调用
 *
 * 使用示例：
 * void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
 * {
 *     if (huart->Instance == USART2) {
 *         JY901S_UART_RxCallback(&jy901s_handle);
 *     }
 * }
 */
void JY901S_UART_RxCallback(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->huart == NULL) {
        return;
    }

    // 解析接收到的字节
    int8_t result = JY901S_ParseByte(handle, handle->rx_byte);

    // 处理解析结果
    if (result == 1) {
        // 数据包解析完成，数据已更新
        // 用户可以在这里添加数据处理逻辑
    } else if (result == -1) {
        // 解析错误，可以在这里添加错误处理逻辑
    }

    // 继续接收下一个字节
    HAL_UART_Receive_IT(handle->huart, &handle->rx_byte, 1);
}

/**
 * @brief UART错误回调函数（用户需要在stm32f1xx_it.c中调用）
 * @param handle JY901S句柄指针
 * @note 这个函数应该在HAL_UART_ErrorCallback中调用
 *
 * 使用示例：
 * void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
 * {
 *     if (huart->Instance == USART2) {
 *         JY901S_UART_ErrorCallback(&jy901s_handle);
 *     }
 * }
 */
void JY901S_UART_ErrorCallback(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->huart == NULL) {
        return;
    }

    // 增加错误计数
    handle->error_count++;

    // 重置解析器状态
    JY901S_ResetParser(handle);

    // 清除UART错误标志
    __HAL_UART_CLEAR_PEFLAG(handle->huart);
    __HAL_UART_CLEAR_FEFLAG(handle->huart);
    __HAL_UART_CLEAR_NEFLAG(handle->huart);
    __HAL_UART_CLEAR_OREFLAG(handle->huart);

    // 重新启动接收
    HAL_UART_Receive_IT(handle->huart, &handle->rx_byte, 1);
}

/**
 * @brief 检查初始化状态
 * @param handle JY901S句柄指针
 * @retval 1: 已初始化, 0: 未初始化
 */
uint8_t JY901S_IsInitialized(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }
    return handle->initialized;
}

/**
 * @brief 重新启动UART接收（用于错误恢复）
 * @param handle JY901S句柄指针
 * @retval 0: 成功, -1: 失败
 */
int8_t JY901S_RestartReceive(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->huart == NULL) {
        return -1;
    }

    // 停止当前接收
    HAL_UART_AbortReceive_IT(handle->huart);

    // 重置解析器
    JY901S_ResetParser(handle);

    // 重新启动接收
    return JY901S_StartReceive(handle);
}

/**
 * @brief 开始零点校准
 * @param handle JY901S句柄指针
 * @retval 0: 成功, -1: 失败
 */
int8_t JY901S_StartCalibration(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return -1;
    }

    // 重置校准数据
    handle->calibration.calibrated = 0;
    handle->calibration.sample_count = 0;
    handle->calibration.acc_sum_x = 0.0f;
    handle->calibration.acc_sum_y = 0.0f;
    handle->calibration.acc_sum_z = 0.0f;
    handle->calibration.angle_sum_x = 0.0f;
    handle->calibration.angle_sum_y = 0.0f;
    handle->calibration.angle_sum_z = 0.0f;

    // 清零当前偏移值
    handle->calibration.acc_offset_x = 0.0f;
    handle->calibration.acc_offset_y = 0.0f;
    handle->calibration.acc_offset_z = 0.0f;
    handle->calibration.angle_offset_x = 0.0f;
    handle->calibration.angle_offset_y = 0.0f;
    handle->calibration.angle_offset_z = 0.0f;

    return 0;
}

/**
 * @brief 校准数据处理（内部函数，在数据更新时调用）
 * @param handle JY901S句柄指针
 * @note 这个函数在ProcessPacket中自动调用
 */
static void JY901S_ProcessCalibration(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->calibration.calibrated) {
        return;
    }

    // 只有当加速度和角度数据都有效时才进行校准
    if (handle->data.acc_valid && handle->data.angle_valid) {
        // 累加数据（使用原始数据，不应用偏移）
        handle->calibration.acc_sum_x += handle->data.acc_x + handle->calibration.acc_offset_x;
        handle->calibration.acc_sum_y += handle->data.acc_y + handle->calibration.acc_offset_y;
        handle->calibration.acc_sum_z += handle->data.acc_z + handle->calibration.acc_offset_z;

        handle->calibration.angle_sum_x += handle->data.angle_x + handle->calibration.angle_offset_x;
        handle->calibration.angle_sum_y += handle->data.angle_y + handle->calibration.angle_offset_y;
        handle->calibration.angle_sum_z += handle->data.angle_z + handle->calibration.angle_offset_z;

        handle->calibration.sample_count++;

        // 检查是否采集足够的样本
        if (handle->calibration.sample_count >= JY901S_CALIBRATION_SAMPLES) {
            // 计算平均值作为零点偏移
            handle->calibration.acc_offset_x = handle->calibration.acc_sum_x / JY901S_CALIBRATION_SAMPLES;
            handle->calibration.acc_offset_y = handle->calibration.acc_sum_y / JY901S_CALIBRATION_SAMPLES;
            handle->calibration.acc_offset_z = handle->calibration.acc_sum_z / JY901S_CALIBRATION_SAMPLES;

            handle->calibration.angle_offset_x = handle->calibration.angle_sum_x / JY901S_CALIBRATION_SAMPLES;
            handle->calibration.angle_offset_y = handle->calibration.angle_sum_y / JY901S_CALIBRATION_SAMPLES;
            handle->calibration.angle_offset_z = handle->calibration.angle_sum_z / JY901S_CALIBRATION_SAMPLES;

            // 标记校准完成
            handle->calibration.calibrated = 1;
        }
    }
}

/**
 * @brief 获取校准进度
 * @param handle JY901S句柄指针
 * @retval 校准进度百分比 (0-100)
 */
uint8_t JY901S_GetCalibrationProgress(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }

    if (handle->calibration.calibrated) {
        return 100;
    }

    return (handle->calibration.sample_count * 100) / JY901S_CALIBRATION_SAMPLES;
}

/**
 * @brief 检查校准是否完成
 * @param handle JY901S句柄指针
 * @retval 1: 校准完成, 0: 校准未完成
 */
uint8_t JY901S_IsCalibrated(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }
    return handle->calibration.calibrated;
}

/**
 * @brief 重置校准数据
 * @param handle JY901S句柄指针
 */
void JY901S_ResetCalibration(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return;
    }

    // 清零所有校准数据
    handle->calibration.acc_offset_x = 0.0f;
    handle->calibration.acc_offset_y = 0.0f;
    handle->calibration.acc_offset_z = 0.0f;
    handle->calibration.angle_offset_x = 0.0f;
    handle->calibration.angle_offset_y = 0.0f;
    handle->calibration.angle_offset_z = 0.0f;
    handle->calibration.calibrated = 0;
    handle->calibration.sample_count = 0;
    handle->calibration.acc_sum_x = 0.0f;
    handle->calibration.acc_sum_y = 0.0f;
    handle->calibration.acc_sum_z = 0.0f;
    handle->calibration.angle_sum_x = 0.0f;
    handle->calibration.angle_sum_y = 0.0f;
    handle->calibration.angle_sum_z = 0.0f;
}

/**
 * @brief 获取数据更新频率（Hz）
 * @param handle JY901S句柄指针
 * @retval 数据更新频率，0表示无数据或错误
 */
float JY901S_GetDataRate(JY901S_Handle_t *handle)
{
    if (handle == NULL || handle->packet_count < 2) {
        return 0.0f;
    }

    uint32_t current_time = HAL_GetTick();
    uint32_t time_diff = current_time - handle->last_update_time;

    if (time_diff == 0) {
        return 0.0f;
    }

    // 计算最近的数据更新频率（简化计算）
    // 实际应用中可以使用滑动窗口平均
    return 1000.0f / time_diff;
}

/**
 * @brief 获取详细的调试信息
 * @param handle JY901S句柄指针
 * @param debug_info 调试信息结构指针
 * @retval 0: 成功, -1: 失败
 */
int8_t JY901S_GetDebugInfo(JY901S_Handle_t *handle, JY901S_DebugInfo_t *debug_info)
{
    if (handle == NULL || debug_info == NULL) {
        return -1;
    }

    debug_info->initialized = handle->initialized;
    debug_info->data_ready = handle->data_ready;
    debug_info->calibrated = handle->calibration.calibrated;
    debug_info->calibration_progress = JY901S_GetCalibrationProgress(handle);
    debug_info->packet_count = handle->packet_count;
    debug_info->error_count = handle->error_count;
    debug_info->checksum_errors = handle->checksum_errors;
    debug_info->data_timeout = JY901S_IsDataTimeout(handle);
    debug_info->data_rate = JY901S_GetDataRate(handle);
    debug_info->last_update_time = handle->last_update_time;
    debug_info->parse_state = handle->parse_state;
    debug_info->data_filter_mask = handle->data_filter_mask;

    return 0;
}

/**
 * @brief 检查传感器数据有效性
 * @param handle JY901S句柄指针
 * @param check_acc 检查加速度数据
 * @param check_gyro 检查陀螺仪数据
 * @param check_angle 检查角度数据
 * @retval 1: 数据有效, 0: 数据无效
 */
uint8_t JY901S_IsDataValid(JY901S_Handle_t *handle, uint8_t check_acc, uint8_t check_gyro, uint8_t check_angle)
{
    if (handle == NULL) {
        return 0;
    }

    // 检查数据是否超时
    if (JY901S_IsDataTimeout(handle)) {
        return 0;
    }

    // 检查指定的数据类型是否有效
    if (check_acc && !handle->data.acc_valid) {
        return 0;
    }

    if (check_gyro && !handle->data.gyro_valid) {
        return 0;
    }

    if (check_angle && !handle->data.angle_valid) {
        return 0;
    }

    return 1;
}

/**
 * @brief 获取传感器数据的时间戳
 * @param handle JY901S句柄指针
 * @retval 数据时间戳（毫秒）
 */
uint32_t JY901S_GetDataTimestamp(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }
    return handle->data.timestamp;
}

/**
 * @brief 获取数据年龄（距离最后更新的时间）
 * @param handle JY901S句柄指针
 * @retval 数据年龄（毫秒）
 */
uint32_t JY901S_GetDataAge(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0xFFFFFFFF;
    }

    uint32_t current_time = HAL_GetTick();
    return current_time - handle->last_update_time;
}

/**
 * @brief 重置统计信息
 * @param handle JY901S句柄指针
 */
void JY901S_ResetStatistics(JY901S_Handle_t *handle)
{
    if (handle != NULL) {
        handle->packet_count = 0;
        handle->error_count = 0;
        handle->checksum_errors = 0;
    }
}

/**
 * @brief 设置自动校准使能
 * @param handle JY901S句柄指针
 * @param enable 1: 启用自动校准, 0: 禁用自动校准
 */
void JY901S_SetAutoCalibrate(JY901S_Handle_t *handle, uint8_t enable)
{
    if (handle != NULL) {
        handle->auto_calibrate = enable;
    }
}

/**
 * @brief 获取自动校准状态
 * @param handle JY901S句柄指针
 * @retval 1: 自动校准启用, 0: 自动校准禁用
 */
uint8_t JY901S_GetAutoCalibrate(JY901S_Handle_t *handle)
{
    if (handle == NULL) {
        return 0;
    }
    return handle->auto_calibrate;
}
