/**
 * @file jy901s.h
 * @brief JY901S九轴姿态传感器STM32驱动库
 * @version 1.0
 * @date 2025-07-16
 * <AUTHOR>
 *
 * @description
 * JY901S是维特智能的九轴姿态传感器，使用WIT私有协议进行通信。
 * 本库专门针对STM32平台优化，提供简洁易用的API接口。
 *
 * 主要功能：
 * - 支持WIT私有协议数据解析（55开头的11字节数据包）
 * - 支持加速度、陀螺仪、角度数据获取
 * - 支持零点校准功能
 * - 支持UART通信（推荐使用UART2连接传感器）
 * - 兼容STM32 HAL库和STM32CubeMX
 *
 * 硬件连接：
 * JY901S VCC  -> STM32 3.3V/5V
 * JY901S GND  -> STM32 GND
 * JY901S TX   -> STM32 UART2_RX (PA3)
 * JY901S RX   -> STM32 UART2_TX (PA2)
 */

#ifndef __JY901S_H__
#define __JY901S_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含必要的头文件 */
#include "mydefine.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* WIT私有协议常量定义 */
#define JY901S_FRAME_HEADER 0x55 // 数据包帧头
#define JY901S_PACKET_SIZE 11    // 数据包总长度（字节）
#define JY901S_DATA_SIZE 8       // 有效数据长度（字节）

/* WIT协议数据类型定义 */
#define JY901S_TYPE_TIME 0x50       // 时间数据
#define JY901S_TYPE_ACC 0x51        // 加速度数据
#define JY901S_TYPE_GYRO 0x52       // 陀螺仪数据
#define JY901S_TYPE_ANGLE 0x53      // 角度数据
#define JY901S_TYPE_MAG 0x54        // 磁力计数据
#define JY901S_TYPE_DSTATUS 0x55    // 端口状态
#define JY901S_TYPE_PRESS 0x56      // 气压高度数据
#define JY901S_TYPE_LONLAT 0x57     // 经纬度数据
#define JY901S_TYPE_GPSV 0x58       // 地速数据
#define JY901S_TYPE_QUATERNION 0x59 // 四元数数据

/* 数据转换常量 */
#define JY901S_ACC_SCALE (16.0f / 32768.0f)    // 加速度量程 ±16g
#define JY901S_GYRO_SCALE (2000.0f / 32768.0f) // 陀螺仪量程 ±2000°/s
#define JY901S_ANGLE_SCALE (180.0f / 32768.0f) // 角度量程 ±180°
#define JY901S_TEMP_SCALE (100.0f / 32768.0f)  // 温度量程

/* 配置参数 */
#define JY901S_CALIBRATION_SAMPLES 100 // 校准时采样数量
#define JY901S_DATA_TIMEOUT_MS 2000    // 数据超时时间（毫秒）
#define JY901S_RX_BUFFER_SIZE 32       // 接收缓冲区大小

/* 解析状态枚举 */
typedef enum
{
JY901S_STATE_HEADER = 0, // 等待帧头
JY901S_STATE_TYPE,       // 等待数据类型
JY901S_STATE_DATA,       // 接收数据
JY901S_STATE_CHECKSUM    // 等待校验和
} JY901S_ParseState_t;

/* 传感器数据结构 */
typedef struct
{
// 加速度数据 (单位: g)
float acc_x;
float acc_y;
float acc_z;

// 陀螺仪数据 (单位: °/s)
float gyro_x;
float gyro_y;
float gyro_z;

// 角度数据 (单位: °)
float angle_x; // Roll  横滚角
float angle_y; // Pitch 俯仰角
float angle_z; // Yaw   偏航角

// 温度数据 (单位: °C)
float temperature;

// 数据有效标志
uint8_t acc_valid;   // 加速度数据有效
uint8_t gyro_valid;  // 陀螺仪数据有效
uint8_t angle_valid; // 角度数据有效

// 数据更新时间戳
uint32_t timestamp;
} JY901S_Data_t;

/* 零点校准数据结构 */
typedef struct
{
// 加速度零点偏移
float acc_offset_x;
float acc_offset_y;
float acc_offset_z;

// 角度零点偏移
float angle_offset_x;
float angle_offset_y;
float angle_offset_z;

// 校准状态
uint8_t calibrated;    // 校准完成标志
uint16_t sample_count; // 当前采样计数

// 校准过程中的累加值
float acc_sum_x, acc_sum_y, acc_sum_z;
float angle_sum_x, angle_sum_y, angle_sum_z;
} JY901S_Calibration_t;

/* JY901S句柄结构 */
typedef struct
{
// UART句柄
UART_HandleTypeDef *huart;

// 接收缓冲区
uint8_t rx_buffer[JY901S_RX_BUFFER_SIZE];
uint8_t rx_byte; // 单字节接收缓冲

// 数据包解析
uint8_t packet_buffer[JY901S_PACKET_SIZE]; // 数据包缓冲区
uint8_t packet_index;                      // 当前数据包索引
uint8_t packet_type;                       // 当前数据包类型
JY901S_ParseState_t parse_state;           // 解析状态

// 传感器数据
JY901S_Data_t data;
JY901S_Calibration_t calibration;

// 状态管理
uint8_t initialized;       // 初始化完成标志
uint8_t data_ready;        // 新数据就绪标志
uint32_t last_update_time; // 最后更新时间

// 统计信息
uint32_t packet_count;    // 接收数据包计数
uint32_t error_count;     // 错误计数
uint16_t checksum_errors; // 校验和错误计数

// 配置参数
uint8_t data_filter_mask; // 数据类型过滤掩码
uint8_t auto_calibrate;   // 自动校准使能
} JY901S_Handle_t;

/* 调试信息结构 */
typedef struct
{
uint8_t initialized;             // 初始化状态
uint8_t data_ready;              // 数据就绪状态
uint8_t calibrated;              // 校准完成状态
uint8_t calibration_progress;    // 校准进度(0-100)
uint32_t packet_count;           // 数据包计数
uint32_t error_count;            // 错误计数
uint16_t checksum_errors;        // 校验和错误计数
uint8_t data_timeout;            // 数据超时状态
float data_rate;                 // 数据更新频率(Hz)
uint32_t last_update_time;       // 最后更新时间
JY901S_ParseState_t parse_state; // 解析器状态
uint8_t data_filter_mask;        // 数据过滤掩码
} JY901S_DebugInfo_t;

/* API函数声明 */

/**
* @brief 初始化JY901S传感器
* @param handle JY901S句柄指针
* @param huart UART句柄指针
* @retval 0: 成功, -1: 失败
*/
int8_t JY901S_Create(JY901S_Handle_t *handle, UART_HandleTypeDef *huart);

/**
* @brief 解析接收到的字节数据（在UART中断中调用）
* @param handle JY901S句柄指针
* @param byte 接收到的字节
* @retval 0: 继续接收, 1: 数据包解析完成, -1: 解析错误
*/
int8_t JY901S_ParseByte(JY901S_Handle_t *handle, uint8_t byte);

/**
* @brief 获取最新的传感器数据
* @param handle JY901S句柄指针
* @retval 传感器数据指针
*/
JY901S_Data_t *JY901S_GetData(JY901S_Handle_t *handle);

/**
* @brief 检查是否有新数据就绪
* @param handle JY901S句柄指针
* @retval 1: 有新数据, 0: 无新数据
*/
uint8_t JY901S_IsDataReady(JY901S_Handle_t *handle);

/**
* @brief 清除数据就绪标志
* @param handle JY901S句柄指针
*/
void JY901S_ClearDataReady(JY901S_Handle_t *handle);

/**
* @brief 开始零点校准
* @param handle JY901S句柄指针
* @retval 0: 成功, -1: 失败
*/
int8_t JY901S_StartCalibration(JY901S_Handle_t *handle);

/**
* @brief 获取校准进度
* @param handle JY901S句柄指针
* @retval 校准进度百分比 (0-100)
*/
uint8_t JY901S_GetCalibrationProgress(JY901S_Handle_t *handle);

/**
* @brief 检查校准是否完成
* @param handle JY901S句柄指针
* @retval 1: 校准完成, 0: 校准未完成
*/
uint8_t JY901S_IsCalibrated(JY901S_Handle_t *handle);

/**
* @brief 重置校准数据
* @param handle JY901S句柄指针
*/
void JY901S_ResetCalibration(JY901S_Handle_t *handle);

/**
* @brief 设置数据类型过滤掩码
* @param handle JY901S句柄指针
* @param mask 过滤掩码 (位0:加速度, 位1:陀螺仪, 位2:角度)
*/
void JY901S_SetDataFilter(JY901S_Handle_t *handle, uint8_t mask);

/**
* @brief 获取统计信息
* @param handle JY901S句柄指针
* @param packet_count 数据包计数指针
* @param error_count 错误计数指针
*/
void JY901S_GetStatistics(JY901S_Handle_t *handle, uint32_t *packet_count, uint32_t *error_count);

/**
* @brief 检查数据是否超时
* @param handle JY901S句柄指针
* @retval 1: 数据超时, 0: 数据正常
*/
uint8_t JY901S_IsDataTimeout(JY901S_Handle_t *handle);

/**
* @brief 启动UART接收中断
* @param handle JY901S句柄指针
* @retval 0: 成功, -1: 失败
*/
int8_t JY901S_StartReceive(JY901S_Handle_t *handle);

/**
* @brief UART接收完成回调函数
* @param handle JY901S句柄指针
*/
void JY901S_UART_RxCallback(JY901S_Handle_t *handle);

/**
* @brief UART错误回调函数
* @param handle JY901S句柄指针
*/
void JY901S_UART_ErrorCallback(JY901S_Handle_t *handle);

/**
* @brief 检查初始化状态
* @param handle JY901S句柄指针
* @retval 1: 已初始化, 0: 未初始化
*/
uint8_t JY901S_IsInitialized(JY901S_Handle_t *handle);

/**
* @brief 重新启动UART接收
* @param handle JY901S句柄指针
* @retval 0: 成功, -1: 失败
*/
int8_t JY901S_RestartReceive(JY901S_Handle_t *handle);

/**
* @brief 获取数据更新频率
* @param handle JY901S句柄指针
* @retval 数据更新频率(Hz)
*/
float JY901S_GetDataRate(JY901S_Handle_t *handle);

/**
* @brief 获取详细的调试信息
* @param handle JY901S句柄指针
* @param debug_info 调试信息结构指针
* @retval 0: 成功, -1: 失败
*/
int8_t JY901S_GetDebugInfo(JY901S_Handle_t *handle, JY901S_DebugInfo_t *debug_info);

/**
* @brief 检查传感器数据有效性
* @param handle JY901S句柄指针
* @param check_acc 检查加速度数据
* @param check_gyro 检查陀螺仪数据
* @param check_angle 检查角度数据
* @retval 1: 数据有效, 0: 数据无效
*/
uint8_t JY901S_IsDataValid(JY901S_Handle_t *handle, uint8_t check_acc, uint8_t check_gyro, uint8_t check_angle);

/**
* @brief 获取传感器数据的时间戳
* @param handle JY901S句柄指针
* @retval 数据时间戳（毫秒）
*/
uint32_t JY901S_GetDataTimestamp(JY901S_Handle_t *handle);

/**
* @brief 获取数据年龄（距离最后更新的时间）
* @param handle JY901S句柄指针
* @retval 数据年龄（毫秒）
*/
uint32_t JY901S_GetDataAge(JY901S_Handle_t *handle);

/**
* @brief 重置统计信息
* @param handle JY901S句柄指针
*/
void JY901S_ResetStatistics(JY901S_Handle_t *handle);

/**
* @brief 设置自动校准使能
* @param handle JY901S句柄指针
* @param enable 1: 启用自动校准, 0: 禁用自动校准
*/
void JY901S_SetAutoCalibrate(JY901S_Handle_t *handle, uint8_t enable);

/**
* @brief 获取自动校准状态
* @param handle JY901S句柄指针
* @retval 1: 自动校准启用, 0: 自动校准禁用
*/
uint8_t JY901S_GetAutoCalibrate(JY901S_Handle_t *handle);

#ifdef __cplusplus
}
#endif

#endif /* __JY901S_H__ */
