--cpu=Cortex-M4.fp.sp
"driver\startup_stm32f407xx.o"
"driver\main.o"
"driver\gpio.o"
"driver\dma.o"
"driver\i2c.o"
"driver\tim.o"
"driver\usart.o"
"driver\stm32f4xx_it.o"
"driver\stm32f4xx_hal_msp.o"
"driver\stm32f4xx_hal_i2c.o"
"driver\stm32f4xx_hal_i2c_ex.o"
"driver\stm32f4xx_hal_rcc.o"
"driver\stm32f4xx_hal_rcc_ex.o"
"driver\stm32f4xx_hal_flash.o"
"driver\stm32f4xx_hal_flash_ex.o"
"driver\stm32f4xx_hal_flash_ramfunc.o"
"driver\stm32f4xx_hal_gpio.o"
"driver\stm32f4xx_hal_dma_ex.o"
"driver\stm32f4xx_hal_dma.o"
"driver\stm32f4xx_hal_pwr.o"
"driver\stm32f4xx_hal_pwr_ex.o"
"driver\stm32f4xx_hal_cortex.o"
"driver\stm32f4xx_hal.o"
"driver\stm32f4xx_hal_exti.o"
"driver\stm32f4xx_hal_tim.o"
"driver\stm32f4xx_hal_tim_ex.o"
"driver\stm32f4xx_hal_uart.o"
"driver\system_stm32f4xx.o"
"driver\hc_sr04.o"
"driver\jy60.o"
"driver\track.o"
"driver\scheduler.o"
"driver\button_app.o"
"driver\led.o"
"driver\uart_app.o"
"driver\control.o"
"driver\beep_app.o"
"driver\jy901s.o"
"driver\ebtn.o"
"driver\encoder.o"
"driver\hardware_iic.o"
"driver\wit_c_sdk.o"
"driver\motor.o"
"driver\pid.o"
"driver\ringbuffer.o"
"driver\uart_driver.o"
"driver\jy901s_driver.o"
--library_type=microlib --strict --scatter "driver\driver.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "driver.map" -o driver\driver.axf