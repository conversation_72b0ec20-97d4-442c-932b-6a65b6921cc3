#ifndef MYDEFINE_H
#define MYDEFINE_H
/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "tim.h"
#include "usart.h"
#include "i2c.h"

/* ========== C 语言头文件 ========== */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 核心调度器头文件 ========== */
#include "scheduler.h"

/* ========== 应用层头文件 ========== */
#include "LED.h"
#include "button_app.h"
#include "Control.h"
#include "uart_app.h"
#include "uart_vofa.h"
#include "hc_sr04.h"
#include "jy60.h"
#include "jy901s.h"
#include "Track.h"
#include "beep_app.h"

/* ========== 组件库头文件 ========== */
#include "ebtn.h"
#include "hardware_iic.h"
#include "PID.h"
#include "motor.h"
#include "Encoder.h"
#include "ringbuffer.h"
#include "uart_driver.h"
#include "jy901s_driver.h"

extern uint8_t led_buf[4];
extern uint8_t beep_status;
extern bool pid_running;

extern float g_line_position_error;
extern float yaw;
extern float yaw1;
extern uint8_t mode_switch;
extern uint8_t pid_mode;
extern uint8_t point_count;
extern uint16_t distance;
/* PID 控制器实例 */
extern PID_T pid_speed_left;  // 左轮速度环
extern PID_T pid_speed_right; // 右轮速度环
extern PID_T pid_line;        // 循迹环
extern PID_T pid_angle;       // 角度环
extern JY901S_Handle_t jy901s_handle;
extern JY901S_Calibration_t jj;
#endif
