#include "jy60.h"
#include "wit_c_sdk.h"

#define ACC_UPDATE 0x01
#define GYRO_UPDATE 0x02
#define ANGLE_UPDATE 0x04
#define MAG_UPDATE 0x08
#define READ_UPDATE 0x80
static volatile char s_cDataUpdate = 0, s_cCmd = 0xff;
const uint32_t c_uiBaud[10] = {0, 4800, 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600};
float fAcc[3], fGyro[3], fAngle[3];
float yaw = 0.0f;
// static void CmdProcess(void);
// static void AutoScanSensor(void);
static void SensorUartSend(uint8_t *p_data, uint32_t uiSize);
static void SensorDataUpdata(uint32_t uiReg, uint32_t uiRegNum);
static void Delayms(uint16_t ucMs);

static void SensorUartSend(uint8_t *p_data, uint32_t uiSize)
{
	HAL_UART_Transmit(&huart2, p_data, uiSize, 999);
}

static void Delayms(uint16_t ucMs)
{
	HAL_Delay(ucMs);
}

static void SensorDataUpdata(uint32_t uiReg, uint32_t uiRegNum)
{
	int i;
	for (i = 0; i < uiRegNum; i++)
	{
		switch (uiReg)
		{
			//            case AX:
			//            case AY:
		case AZ:
			s_cDataUpdate |= ACC_UPDATE;
			break;
			//            case GX:
			//            case GY:
		case GZ:
			s_cDataUpdate |= GYRO_UPDATE;
			break;
			//            case HX:
			//            case HY:
		case HZ:
			s_cDataUpdate |= MAG_UPDATE;
			break;
			//            case Roll:
			//            case Pitch:
		case Yaw:
			s_cDataUpdate |= ANGLE_UPDATE;
			break;
		default:
			s_cDataUpdate |= READ_UPDATE;
			break;
		}
		uiReg++;
	}
}

void jy60_init(void)
{
	WitInit(WIT_PROTOCOL_NORMAL, 0x50);
	WitSerialWriteRegister(SensorUartSend);
	WitRegisterCallBack(SensorDataUpdata);
	WitDelayMsRegister(Delayms);
	// 添加偏航角校准
	jy60_reset_z_angle(); // 开始磁力计校准（影响偏航角精度）
	HAL_Delay(500);		  // 校准期间旋转设备
						  // WitStopMagCali();  // 停止校准并保存
}

void jy60_read(void)
{
	if (s_cDataUpdate)
	{
		for (uint8_t i = 0; i < 3; i++)
		{
			fAcc[i] = sReg[AX + i] / 32768.0f * 16.0f;
			fGyro[i] = sReg[GX + i] / 32768.0f * 2000.0f;
			fAngle[i] = sReg[Roll + i] / 32768.0f * 180.0f;
		}
		//			uint8_t strff[21];
		//
		//			sprintf(strff,"%.2f",fAngle[2]);
		//
		//			OLED_ShowStr(0,3,strff,1);
		// 			if(s_cDataUpdate & ACC_UPDATE)
		// 			{
		// //				my_printf(&huart4,"acc:%.3f %.3f %.3f\r\n", fAcc[0], fAcc[1], fAcc[2]);
		// 				s_cDataUpdate &= ~ACC_UPDATE;
		// 			}
		// 			if(s_cDataUpdate & GYRO_UPDATE)
		// 			{
		// 				//my_printf(&huart4,"gyro:%.3f %.3f %.3f\r\n", fGyro[0], fGyro[1], fGyro[2]);
		// 				s_cDataUpdate &= ~GYRO_UPDATE;
		// 			}
		// 			if(s_cDataUpdate & ANGLE_UPDATE)
		// 			{
		// //				my_printf(&huart4,"angle:%.3f %.3f %.3f\r\n", fAngle[0], fAngle[1], fAngle[2]);
		// 				yaw = convert_to_continuous_yaw(fAngle[2]);
		// 				s_cDataUpdate &= ~ANGLE_UPDATE;
		// 			}
		// 			if(s_cDataUpdate & MAG_UPDATE)
		// 			{
		// 				//my_printf(&huart4,"mag:%d %d %d\r\n", sReg[HX], sReg[HY], sReg[HZ]);
		// 				s_cDataUpdate &= ~MAG_UPDATE;
		// 			}
	}
}
// 使用静态变量来保存上一次的状态
static float g_last_yaw = 0.0f;
static int g_revolution_count = 0;
static bool g_is_yaw_initialized = false;
/**
 * @brief 将一个在[-180, 180]范围内的yaw角度转换为连续的角度值。
 *
 * @param current_yaw 从传感器读取的当前yaw值 (-180 to 180)。
 * @return float 连续的yaw角度值 (例如 370, -450 等)。
 */
float convert_to_continuous_yaw(float current_yaw)
{
	// 首次调用初始化
	if (!g_is_yaw_initialized)
	{
		g_last_yaw = current_yaw;
		g_is_yaw_initialized = true;
		g_revolution_count = 0;
		return current_yaw; // 第一次直接返回当前值
	}

	// 计算角度差值
	float diff = current_yaw - g_last_yaw;

	// 检测跳变：从+180跳到-180 (向右转过界)
	if (diff < -180.0f)
	{
		g_revolution_count++; // 圈数+1
	}
	// 检测跳变：从-180跳到+180 (向左转过界)
	else if (diff > 180.0f)
	{
		g_revolution_count--; // 圈数-1
	}

	// 更新上次角度
	g_last_yaw = current_yaw;

	// 计算连续角度
	float continuous_yaw = current_yaw + (float)g_revolution_count * 360.0f;

	return continuous_yaw;
}

/**
 * @brief Z轴角度归零函数
 * @note 发送指令: FF AA 52 来重置Z轴角度为0
 * @return int32_t 返回操作结果
 */
int32_t jy60_reset_z_angle(void)
{
	uint8_t reset_cmd[3] = {0xFF, 0xAA, 0x52}; // Z轴角度归零指令
	HAL_UART_Transmit(&huart2, reset_cmd, 3, 1000);
	HAL_Delay(100); // 等待传感器处理指令

	// 重置本地连续角度计算状态
	g_last_yaw = 0.0f;
	g_revolution_count = 0;
	g_is_yaw_initialized = false;
	yaw = 0.0f;

	return WIT_HAL_OK;
}

void jy60_task(void)
{
	// jy60_read();
	// if (s_cDataUpdate & ANGLE_UPDATE)
	// {
	// 	//  yaw = fAngle[2];
	// 	yaw = convert_to_continuous_yaw(fAngle[2]);
	// 	s_cDataUpdate &= ~ANGLE_UPDATE;
	// 	// my_printf(&huart4,"yaw:%.2f fAngle[2]:%.2f\r\n", yaw,fAngle[2]);
	// }
}
