#include "jy901s.h"

JY901S_Handle_t jy901s_handle; // JY901S句柄
JY901S_Calibration_t jj;
float yaw1;

int JY901S_Init(void)
{
    if (JY901S_Create(&jy901s_handle, &huart2) != 0)
    {
        my_printf(&huart4, "JY901S初始化失败!\r\n");
        return 1;
    }
    // 只接收角度
    JY901S_SetDataFilter(&jy901s_handle, 0x04); // 位0:加速度, 位2:角度
    // 手动开始校准
    JY901S_StartCalibration(&jy901s_handle);
    // 监控校准进度
    while (!JY901S_IsCalibrated(&jy901s_handle))
    {
        uint8_t progress = JY901S_GetCalibrationProgress(&jy901s_handle);
        my_printf(&huart4, "校准进度: %d%%\r\n", progress);
        HAL_Delay(100);
    }
    my_printf(&huart4, "校准完成！\r\n");
    return 0;
}

void JY901S_Task(void)
{
    // 检查是否有新数据
    if (JY901S_IsDataReady(&jy901s_handle))
    {

        // 获取传感器数据
        JY901S_Data_t *data = JY901S_GetData(&jy901s_handle);
        yaw1 = data->angle_z;
        // 清除数据就绪标志
        JY901S_ClearDataReady(&jy901s_handle);
    }
}
