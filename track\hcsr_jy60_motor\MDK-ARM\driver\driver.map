Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM1_TRG_COM_TIM11_IRQHandler) for TIM1_TRG_COM_TIM11_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM11_Init) for MX_TIM11_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Init) for Scheduler_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Run) for Scheduler_Run
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Base_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    tim.o(i.HAL_TIM_Base_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_IC_CaptureCallback) refers to hc_sr04.o(i.Hcsr04TimIcIsr) for Hcsr04TimIcIsr
    tim.o(i.HAL_TIM_IC_CaptureCallback) refers to tim.o(.bss) for .bss
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM11_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(i.MX_TIM11_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(i.MX_TIM11_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM4_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_ErrorCallback) refers to jy901s_driver.o(i.JY901S_UART_ErrorCallback) for JY901S_UART_ErrorCallback
    usart.o(i.HAL_UART_ErrorCallback) refers to jy901s.o(.bss) for jy901s_handle
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.HAL_UART_RxCpltCallback) refers to jy901s_driver.o(i.JY901S_UART_RxCallback) for JY901S_UART_RxCallback
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for .data
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_RxCpltCallback) refers to jy901s.o(.bss) for jy901s_handle
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.data) for .data
    usart.o(i.Serial_GetRxFlag) refers to usart.o(.data) for .data
    usart.o(i.Serial_Printf) refers to printfa.o(i.__0vsprintf) for vsprintf
    usart.o(i.Serial_Printf) refers to usart.o(i.Serial_SendString) for Serial_SendString
    usart.o(i.Serial_SendString) refers to strlen.o(.text) for strlen
    usart.o(i.Serial_SendString) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.Serial_SendString) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM1_TRG_COM_TIM11_IRQHandler) refers to tim.o(.bss) for htim1
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.TIM4_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM4_IRQHandler) refers to tim.o(.bss) for htim4
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to scheduler.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    hc_sr04.o(i.Hcsr04Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    hc_sr04.o(i.Hcsr04Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) for HAL_TIM_IC_Start_IT
    hc_sr04.o(i.Hcsr04Init) refers to hc_sr04.o(.bss) for .bss
    hc_sr04.o(i.Hcsr04Read) refers to hc_sr04.o(.bss) for .bss
    hc_sr04.o(i.Hcsr04Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    hc_sr04.o(i.Hcsr04Start) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to dfltui.o(.text) for __aeabi_ui2d
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to ddiv.o(.text) for __aeabi_ddiv
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to dmul.o(.text) for __aeabi_dmul
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to d2f.o(.text) for __aeabi_d2f
    hc_sr04.o(i.Hcsr04TimIcIsr) refers to hc_sr04.o(.bss) for .bss
    hc_sr04.o(i.Hcsr04TimOverflowIsr) refers to hc_sr04.o(.bss) for .bss
    hc_sr04.o(i.Hcsr04_read_distance) refers to hc_sr04.o(i.Hcsr04Start) for Hcsr04Start
    hc_sr04.o(i.Hcsr04_read_distance) refers to hc_sr04.o(i.Hcsr04Read) for Hcsr04Read
    jy60.o(i.Delayms) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    jy60.o(i.SensorDataUpdata) refers to jy60.o(.data) for .data
    jy60.o(i.SensorUartSend) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    jy60.o(i.SensorUartSend) refers to usart.o(.bss) for huart2
    jy60.o(i.convert_to_continuous_yaw) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_init) refers to wit_c_sdk.o(i.WitInit) for WitInit
    jy60.o(i.jy60_init) refers to wit_c_sdk.o(i.WitSerialWriteRegister) for WitSerialWriteRegister
    jy60.o(i.jy60_init) refers to wit_c_sdk.o(i.WitRegisterCallBack) for WitRegisterCallBack
    jy60.o(i.jy60_init) refers to wit_c_sdk.o(i.WitDelayMsRegister) for WitDelayMsRegister
    jy60.o(i.jy60_init) refers to jy60.o(i.jy60_reset_z_angle) for jy60_reset_z_angle
    jy60.o(i.jy60_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    jy60.o(i.jy60_init) refers to jy60.o(i.SensorUartSend) for SensorUartSend
    jy60.o(i.jy60_init) refers to jy60.o(i.SensorDataUpdata) for SensorDataUpdata
    jy60.o(i.jy60_init) refers to jy60.o(i.Delayms) for Delayms
    jy60.o(i.jy60_read) refers to jy60.o(.data) for .data
    jy60.o(i.jy60_read) refers to wit_c_sdk.o(.bss) for sReg
    jy60.o(i.jy60_read) refers to jy60.o(.bss) for .bss
    jy60.o(i.jy60_reset_z_angle) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    jy60.o(i.jy60_reset_z_angle) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    jy60.o(i.jy60_reset_z_angle) refers to usart.o(.bss) for huart2
    jy60.o(i.jy60_reset_z_angle) refers to jy60.o(.data) for .data
    track.o(i.Gray_Task) refers to track.o(i.read_line_sensors) for read_line_sensors
    track.o(i.Gray_Task) refers to track.o(.data) for .data
    track.o(i.Gray_Task) refers to track.o(.constdata) for .constdata
    track.o(i.PrintTrackerStatus) refers to uart_app.o(i.my_printf) for my_printf
    track.o(i.PrintTrackerStatus) refers to f2d.o(.text) for __aeabi_f2d
    track.o(i.PrintTrackerStatus) refers to usart.o(.bss) for huart4
    track.o(i.PrintTrackerStatus) refers to jy901s.o(.data) for yaw1
    track.o(i.read_line_sensors) refers to hardware_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    track.o(i.read_line_sensors) refers to track.o(.data) for .data
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to hc_sr04.o(i.Hcsr04TimOverflowIsr) for Hcsr04TimOverflowIsr
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder.o(i.Encoder_Task) for Encoder_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to track.o(i.Gray_Task) for Gray_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to control.o(i.PID_Task) for PID_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to jy901s.o(i.JY901S_Task) for JY901S_Task
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler.o(i.task_switch) for task_switch
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim11
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to led.o(.data) for led_buf
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler.o(.data) for .data
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to beep_app.o(.data) for beep_status
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder.o(.bss) for left_encoder
    scheduler.o(i.HAL_TIM_PeriodElapsedCallback) refers to track.o(.data) for Digital
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(i.System_Init) for System_Init
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(.data) for .data
    scheduler.o(i.Scheduler_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.Scheduler_Run) refers to scheduler.o(.data) for .data
    scheduler.o(i.System_Init) refers to motor.o(i.Motor_Init) for Motor_Init
    scheduler.o(i.System_Init) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    scheduler.o(i.System_Init) refers to hc_sr04.o(i.Hcsr04Init) for Hcsr04Init
    scheduler.o(i.System_Init) refers to button_app.o(i.button_init) for button_init
    scheduler.o(i.System_Init) refers to control.o(i.PID_Init) for PID_Init
    scheduler.o(i.System_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    scheduler.o(i.System_Init) refers to jy901s.o(i.JY901S_Init) for JY901S_Init
    scheduler.o(i.System_Init) refers to tim.o(.bss) for htim11
    scheduler.o(i.task_switch) refers to motor.o(i.Motor_Stop) for Motor_Stop
    scheduler.o(i.task_switch) refers to dfltui.o(.text) for __aeabi_ui2d
    scheduler.o(i.task_switch) refers to dmul.o(.text) for __aeabi_dmul
    scheduler.o(i.task_switch) refers to dadd.o(.text) for __aeabi_dadd
    scheduler.o(i.task_switch) refers to d2f.o(.text) for __aeabi_d2f
    scheduler.o(i.task_switch) refers to pid.o(i.pid_set_target) for pid_set_target
    scheduler.o(i.task_switch) refers to pid.o(i.pid_reset) for pid_reset
    scheduler.o(i.task_switch) refers to beep_app.o(.data) for beep_status
    scheduler.o(i.task_switch) refers to led.o(.data) for led_buf
    scheduler.o(i.task_switch) refers to scheduler.o(.data) for .data
    scheduler.o(i.task_switch) refers to control.o(.data) for pid_running
    scheduler.o(i.task_switch) refers to motor.o(.bss) for left_motor
    scheduler.o(i.task_switch) refers to control.o(.bss) for pid_angle
    scheduler.o(.data) refers to led.o(i.Led_Task) for Led_Task
    scheduler.o(.data) refers to beep_app.o(i.beep_task) for beep_task
    scheduler.o(.data) refers to button_app.o(i.button_task) for button_task
    scheduler.o(.data) refers to track.o(i.PrintTrackerStatus) for PrintTrackerStatus
    button_app.o(i.button_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    button_app.o(i.button_init) refers to button_app.o(i.my_handle_key_event) for my_handle_key_event
    button_app.o(i.button_init) refers to button_app.o(i.my_get_key_state) for my_get_key_state
    button_app.o(i.button_init) refers to button_app.o(.data) for .data
    button_app.o(i.button_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    button_app.o(i.button_task) refers to stm32f4xx_hal.o(.data) for uwTick
    button_app.o(i.my_get_key_state) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    button_app.o(i.my_handle_key_event) refers to scheduler.o(.data) for mode_switch
    button_app.o(i.my_handle_key_event) refers to control.o(.data) for pid_running
    button_app.o(.data) refers to button_app.o(.constdata) for default_param_normal
    led.o(i.Led_Display) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.Led_Display) refers to led.o(.data) for .data
    led.o(i.Led_Task) refers to led.o(i.Led_Display) for Led_Display
    led.o(i.Led_Task) refers to led.o(.data) for .data
    uart_app.o(i.Uart_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(i.Uart_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Init) refers to usart.o(.bss) for huart4
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart_Task) refers to uart_app.o(i.my_printf) for my_printf
    uart_app.o(i.Uart_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart_data_buffer
    uart_app.o(i.Uart_Task) refers to usart.o(.bss) for huart4
    uart_app.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_app.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    control.o(i.Angle_PID_control) refers to pid.o(i.pid_calculate_incremental) for pid_calculate_incremental
    control.o(i.Angle_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    control.o(i.Angle_PID_control) refers to dflti.o(.text) for __aeabi_i2d
    control.o(i.Angle_PID_control) refers to dmul.o(.text) for __aeabi_dmul
    control.o(i.Angle_PID_control) refers to dadd.o(.text) for __aeabi_dadd
    control.o(i.Angle_PID_control) refers to d2f.o(.text) for __aeabi_d2f
    control.o(i.Angle_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    control.o(i.Angle_PID_control) refers to jy901s.o(.data) for yaw1
    control.o(i.Angle_PID_control) refers to control.o(.bss) for .bss
    control.o(i.Angle_PID_control) refers to control.o(.data) for .data
    control.o(i.Line_PID_control) refers to pid.o(i.pid_calculate_incremental) for pid_calculate_incremental
    control.o(i.Line_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    control.o(i.Line_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    control.o(i.Line_PID_control) refers to track.o(.data) for g_line_position_error
    control.o(i.Line_PID_control) refers to control.o(.bss) for .bss
    control.o(i.Line_PID_control) refers to control.o(.data) for .data
    control.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    control.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    control.o(i.PID_Init) refers to control.o(.data) for .data
    control.o(i.PID_Init) refers to control.o(.bss) for .bss
    control.o(i.PID_Task) refers to control.o(i.Angle_PID_control) for Angle_PID_control
    control.o(i.PID_Task) refers to control.o(i.Line_PID_control) for Line_PID_control
    control.o(i.PID_Task) refers to pid.o(i.pid_calculate_incremental) for pid_calculate_incremental
    control.o(i.PID_Task) refers to pid.o(i.pid_constrain) for pid_constrain
    control.o(i.PID_Task) refers to motor.o(i.Motor_Set_Speed) for Motor_Set_Speed
    control.o(i.PID_Task) refers to control.o(.data) for .data
    control.o(i.PID_Task) refers to scheduler.o(.data) for distance
    control.o(i.PID_Task) refers to encoder.o(.bss) for left_encoder
    control.o(i.PID_Task) refers to control.o(.bss) for .bss
    control.o(i.PID_Task) refers to motor.o(.bss) for left_motor
    control.o(i.Test_Motor_Sync) refers to motor.o(i.Motor_Set_Speed) for Motor_Set_Speed
    control.o(i.Test_Motor_Sync) refers to f2d.o(.text) for __aeabi_f2d
    control.o(i.Test_Motor_Sync) refers to uart_app.o(i.my_printf) for my_printf
    control.o(i.Test_Motor_Sync) refers to motor.o(.bss) for left_motor
    control.o(i.Test_Motor_Sync) refers to encoder.o(.bss) for right_encoder
    control.o(i.Test_Motor_Sync) refers to usart.o(.bss) for huart4
    beep_app.o(i.beep_task) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    beep_app.o(i.beep_task) refers to beep_app.o(.data) for .data
    jy901s.o(i.JY901S_Init) refers to jy901s_driver.o(i.JY901S_Create) for JY901S_Create
    jy901s.o(i.JY901S_Init) refers to uart_app.o(i.my_printf) for my_printf
    jy901s.o(i.JY901S_Init) refers to jy901s_driver.o(i.JY901S_SetDataFilter) for JY901S_SetDataFilter
    jy901s.o(i.JY901S_Init) refers to jy901s_driver.o(i.JY901S_StartCalibration) for JY901S_StartCalibration
    jy901s.o(i.JY901S_Init) refers to jy901s_driver.o(i.JY901S_GetCalibrationProgress) for JY901S_GetCalibrationProgress
    jy901s.o(i.JY901S_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    jy901s.o(i.JY901S_Init) refers to jy901s_driver.o(i.JY901S_IsCalibrated) for JY901S_IsCalibrated
    jy901s.o(i.JY901S_Init) refers to usart.o(.bss) for huart2
    jy901s.o(i.JY901S_Init) refers to jy901s.o(.bss) for .bss
    jy901s.o(i.JY901S_Task) refers to jy901s_driver.o(i.JY901S_IsDataReady) for JY901S_IsDataReady
    jy901s.o(i.JY901S_Task) refers to jy901s_driver.o(i.JY901S_GetData) for JY901S_GetData
    jy901s.o(i.JY901S_Task) refers to jy901s_driver.o(i.JY901S_ClearDataReady) for JY901S_ClearDataReady
    jy901s.o(i.JY901S_Task) refers to jy901s.o(.bss) for .bss
    jy901s.o(i.JY901S_Task) refers to jy901s.o(.data) for .data
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_init) refers to memseta.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for .bss
    encoder.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder.o(i.Encoder_Init) refers to encoder.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder.o(i.Encoder_Init) refers to tim.o(.bss) for htim3
    encoder.o(i.Encoder_Init) refers to encoder.o(.bss) for .bss
    encoder.o(i.Encoder_Task) refers to encoder.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder.o(i.Encoder_Task) refers to encoder.o(.bss) for .bss
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c2
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    wit_c_sdk.o(i.CopeWitData) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.CopeWitData) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitCanDataIn) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitCanWriteRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitDeInit) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitDelayMsRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitI2cFuncRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitInit) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitReadReg) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitRegisterCallBack) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.CopeWitData) for CopeWitData
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitSerialDataIn) refers to memcpya.o(.text) for __aeabi_memcpy
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSerialDataIn) refers to wit_c_sdk.o(.bss) for .bss
    wit_c_sdk.o(i.WitSerialWriteRegister) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetBandwidth) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetCanBaud) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetContent) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetOutputRate) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.CheckRange) for CheckRange
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitSetUartBaud) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartAccCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStartMagCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopAccCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(i.WitWriteReg) for WitWriteReg
    wit_c_sdk.o(i.WitStopMagCali) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(i.__CRC16) for __CRC16
    wit_c_sdk.o(i.WitWriteReg) refers to wit_c_sdk.o(.data) for .data
    wit_c_sdk.o(i.__CRC16) refers to wit_c_sdk.o(.constdata) for .constdata
    motor.o(i.Motor_Config_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.Motor_Config_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor.o(i.Motor_Init) refers to motor.o(i.Motor_Config_Init) for Motor_Config_Init
    motor.o(i.Motor_Init) refers to tim.o(.bss) for htim4
    motor.o(i.Motor_Init) refers to motor.o(.bss) for .bss
    motor.o(i.Motor_Set_Speed) refers to motor.o(i.Motor_Limit_Speed) for Motor_Limit_Speed
    motor.o(i.Motor_Set_Speed) refers to motor.o(i.Motor_Dead_Compensation) for Motor_Dead_Compensation
    motor.o(i.Motor_Set_Speed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.Motor_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_set_target) refers to pid.o(i.pid_init) for pid_init
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss) for .bss
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart4
    uart_driver.o(i.Uart_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(i.Uart_Printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    jy901s_driver.o(i.JY901S_Create) refers to memseta.o(.text) for __aeabi_memclr4
    jy901s_driver.o(i.JY901S_Create) refers to jy901s_driver.o(i.JY901S_InitDataStructure) for JY901S_InitDataStructure
    jy901s_driver.o(i.JY901S_Create) refers to jy901s_driver.o(i.JY901S_ResetParser) for JY901S_ResetParser
    jy901s_driver.o(i.JY901S_Create) refers to jy901s_driver.o(i.JY901S_StartReceive) for JY901S_StartReceive
    jy901s_driver.o(i.JY901S_GetDataAge) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(i.JY901S_GetDataRate) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(i.JY901S_GetDebugInfo) refers to jy901s_driver.o(i.JY901S_GetCalibrationProgress) for JY901S_GetCalibrationProgress
    jy901s_driver.o(i.JY901S_GetDebugInfo) refers to jy901s_driver.o(i.JY901S_IsDataTimeout) for JY901S_IsDataTimeout
    jy901s_driver.o(i.JY901S_GetDebugInfo) refers to jy901s_driver.o(i.JY901S_GetDataRate) for JY901S_GetDataRate
    jy901s_driver.o(i.JY901S_InitDataStructure) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(i.JY901S_IsDataTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(i.JY901S_IsDataValid) refers to jy901s_driver.o(i.JY901S_IsDataTimeout) for JY901S_IsDataTimeout
    jy901s_driver.o(i.JY901S_ParseByte) refers to jy901s_driver.o(i.JY901S_ResetParser) for JY901S_ResetParser
    jy901s_driver.o(i.JY901S_ParseByte) refers to jy901s_driver.o(i.JY901S_ProcessPacket) for JY901S_ProcessPacket
    jy901s_driver.o(i.JY901S_ProcessPacket) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    jy901s_driver.o(i.JY901S_ProcessPacket) refers to jy901s_driver.o(i.JY901S_ProcessCalibration) for JY901S_ProcessCalibration
    jy901s_driver.o(i.JY901S_RestartReceive) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) for HAL_UART_AbortReceive_IT
    jy901s_driver.o(i.JY901S_RestartReceive) refers to jy901s_driver.o(i.JY901S_ResetParser) for JY901S_ResetParser
    jy901s_driver.o(i.JY901S_RestartReceive) refers to jy901s_driver.o(i.JY901S_StartReceive) for JY901S_StartReceive
    jy901s_driver.o(i.JY901S_StartReceive) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    jy901s_driver.o(i.JY901S_UART_ErrorCallback) refers to jy901s_driver.o(i.JY901S_ResetParser) for JY901S_ResetParser
    jy901s_driver.o(i.JY901S_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    jy901s_driver.o(i.JY901S_UART_RxCallback) refers to jy901s_driver.o(i.JY901S_ParseByte) for JY901S_ParseByte
    jy901s_driver.o(i.JY901S_UART_RxCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (88 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (76 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (192 bytes).
    Removing usart.o(i.Serial_GetRxFlag), (24 bytes).
    Removing usart.o(i.Serial_Printf), (28 bytes).
    Removing usart.o(i.Serial_SendString), (32 bytes).
    Removing usart.o(i.fputc), (24 bytes).
    Removing usart.o(.data), (2 bytes).
    Removing usart.o(.data), (1 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (86 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing hc_sr04.o(.rev16_text), (4 bytes).
    Removing hc_sr04.o(.revsh_text), (4 bytes).
    Removing hc_sr04.o(.rrx_text), (6 bytes).
    Removing hc_sr04.o(i.Hcsr04Read), (40 bytes).
    Removing hc_sr04.o(i.Hcsr04Start), (40 bytes).
    Removing hc_sr04.o(i.Hcsr04_read_distance), (14 bytes).
    Removing jy60.o(.rev16_text), (4 bytes).
    Removing jy60.o(.revsh_text), (4 bytes).
    Removing jy60.o(.rrx_text), (6 bytes).
    Removing jy60.o(i.Delayms), (4 bytes).
    Removing jy60.o(i.SensorDataUpdata), (76 bytes).
    Removing jy60.o(i.SensorUartSend), (20 bytes).
    Removing jy60.o(i.convert_to_continuous_yaw), (100 bytes).
    Removing jy60.o(i.jy60_init), (56 bytes).
    Removing jy60.o(i.jy60_read), (164 bytes).
    Removing jy60.o(i.jy60_reset_z_angle), (68 bytes).
    Removing jy60.o(i.jy60_task), (2 bytes).
    Removing jy60.o(.bss), (36 bytes).
    Removing jy60.o(.constdata), (40 bytes).
    Removing jy60.o(.data), (16 bytes).
    Removing track.o(.rev16_text), (4 bytes).
    Removing track.o(.revsh_text), (4 bytes).
    Removing track.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing button_app.o(.rev16_text), (4 bytes).
    Removing button_app.o(.revsh_text), (4 bytes).
    Removing button_app.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(i.Uart_Init), (56 bytes).
    Removing uart_app.o(i.Uart_Task), (84 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing control.o(.rrx_text), (6 bytes).
    Removing control.o(i.Test_Motor_Sync), (124 bytes).
    Removing beep_app.o(.rev16_text), (4 bytes).
    Removing beep_app.o(.revsh_text), (4 bytes).
    Removing beep_app.o(.rrx_text), (6 bytes).
    Removing jy901s.o(.rev16_text), (4 bytes).
    Removing jy901s.o(.revsh_text), (4 bytes).
    Removing jy901s.o(.rrx_text), (6 bytes).
    Removing jy901s.o(.bss), (52 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (40 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (72 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_key_id), (64 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (24 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (16 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (16 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (116 bytes).
    Removing ebtn.o(i.ebtn_register), (52 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (44 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (36 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing wit_c_sdk.o(i.CheckRange), (16 bytes).
    Removing wit_c_sdk.o(i.CopeWitData), (180 bytes).
    Removing wit_c_sdk.o(i.WitCanDataIn), (68 bytes).
    Removing wit_c_sdk.o(i.WitCanWriteRegister), (20 bytes).
    Removing wit_c_sdk.o(i.WitDeInit), (28 bytes).
    Removing wit_c_sdk.o(i.WitDelayMsRegister), (20 bytes).
    Removing wit_c_sdk.o(i.WitI2cFuncRegister), (28 bytes).
    Removing wit_c_sdk.o(i.WitInit), (28 bytes).
    Removing wit_c_sdk.o(i.WitReadReg), (320 bytes).
    Removing wit_c_sdk.o(i.WitRegisterCallBack), (20 bytes).
    Removing wit_c_sdk.o(i.WitSerialDataIn), (256 bytes).
    Removing wit_c_sdk.o(i.WitSerialWriteRegister), (20 bytes).
    Removing wit_c_sdk.o(i.WitSetBandwidth), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetCanBaud), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetContent), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetOutputRate), (84 bytes).
    Removing wit_c_sdk.o(i.WitSetUartBaud), (84 bytes).
    Removing wit_c_sdk.o(i.WitStartAccCali), (60 bytes).
    Removing wit_c_sdk.o(i.WitStartMagCali), (60 bytes).
    Removing wit_c_sdk.o(i.WitStopAccCali), (60 bytes).
    Removing wit_c_sdk.o(i.WitStopMagCali), (60 bytes).
    Removing wit_c_sdk.o(i.WitWriteReg), (232 bytes).
    Removing wit_c_sdk.o(i.__CRC16), (44 bytes).
    Removing wit_c_sdk.o(.bss), (544 bytes).
    Removing wit_c_sdk.o(.constdata), (512 bytes).
    Removing wit_c_sdk.o(.data), (40 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing pid.o(i.pid_app_limit_integral), (36 bytes).
    Removing pid.o(i.pid_calculate_positional), (102 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (6 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_get), (110 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_init), (38 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (92 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (8 bytes).
    Removing uart_driver.o(.rev16_text), (4 bytes).
    Removing uart_driver.o(.revsh_text), (4 bytes).
    Removing uart_driver.o(.rrx_text), (6 bytes).
    Removing uart_driver.o(i.Uart_Printf), (50 bytes).
    Removing uart_driver.o(.bss), (128 bytes).
    Removing uart_driver.o(.bss), (128 bytes).
    Removing jy901s_driver.o(.rev16_text), (4 bytes).
    Removing jy901s_driver.o(.revsh_text), (4 bytes).
    Removing jy901s_driver.o(.rrx_text), (6 bytes).
    Removing jy901s_driver.o(i.JY901S_GetAutoCalibrate), (10 bytes).
    Removing jy901s_driver.o(i.JY901S_GetDataAge), (24 bytes).
    Removing jy901s_driver.o(i.JY901S_GetDataRate), (72 bytes).
    Removing jy901s_driver.o(i.JY901S_GetDataTimestamp), (8 bytes).
    Removing jy901s_driver.o(i.JY901S_GetDebugInfo), (98 bytes).
    Removing jy901s_driver.o(i.JY901S_GetStatistics), (24 bytes).
    Removing jy901s_driver.o(i.JY901S_IsDataTimeout), (30 bytes).
    Removing jy901s_driver.o(i.JY901S_IsDataValid), (60 bytes).
    Removing jy901s_driver.o(i.JY901S_IsInitialized), (10 bytes).
    Removing jy901s_driver.o(i.JY901S_ResetCalibration), (72 bytes).
    Removing jy901s_driver.o(i.JY901S_ResetStatistics), (16 bytes).
    Removing jy901s_driver.o(i.JY901S_RestartReceive), (36 bytes).
    Removing jy901s_driver.o(i.JY901S_SetAutoCalibrate), (10 bytes).

591 unused section(s) (total 45179 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\APP\Control.c                         0x00000000   Number         0  control.o ABSOLUTE
    ..\APP\LED.c                             0x00000000   Number         0  led.o ABSOLUTE
    ..\APP\Track.c                           0x00000000   Number         0  track.o ABSOLUTE
    ..\APP\beep_app.c                        0x00000000   Number         0  beep_app.o ABSOLUTE
    ..\APP\button_app.c                      0x00000000   Number         0  button_app.o ABSOLUTE
    ..\APP\hc_sr04.c                         0x00000000   Number         0  hc_sr04.o ABSOLUTE
    ..\APP\jy60.c                            0x00000000   Number         0  jy60.o ABSOLUTE
    ..\APP\jy901s.c                          0x00000000   Number         0  jy901s.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Component\Grayscale\hardware_iic.c    0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\Component\PID\PID.c                   0x00000000   Number         0  pid.o ABSOLUTE
    ..\Component\Uart\ringbuffer.c           0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\Component\Uart\uart_driver.c          0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\Component\ebtn\ebtn.c                 0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Component\encoder\Encoder.c           0x00000000   Number         0  encoder.o ABSOLUTE
    ..\Component\jy60\wit_c_sdk.c            0x00000000   Number         0  wit_c_sdk.o ABSOLUTE
    ..\Component\jy901s\jy901s_driver.c      0x00000000   Number         0  jy901s_driver.o ABSOLUTE
    ..\Component\motor\Motor.c               0x00000000   Number         0  motor.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\APP\\Control.c                       0x00000000   Number         0  control.o ABSOLUTE
    ..\\APP\\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    ..\\APP\\Track.c                         0x00000000   Number         0  track.o ABSOLUTE
    ..\\APP\\beep_app.c                      0x00000000   Number         0  beep_app.o ABSOLUTE
    ..\\APP\\button_app.c                    0x00000000   Number         0  button_app.o ABSOLUTE
    ..\\APP\\hc_sr04.c                       0x00000000   Number         0  hc_sr04.o ABSOLUTE
    ..\\APP\\jy60.c                          0x00000000   Number         0  jy60.o ABSOLUTE
    ..\\APP\\jy901s.c                        0x00000000   Number         0  jy901s.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\Component\\Grayscale\\hardware_iic.c 0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\Component\\Uart\\uart_driver.c       0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\\Component\\encoder\\Encoder.c        0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\Component\\jy901s\\jy901s_driver.c   0x00000000   Number         0  jy901s_driver.o ABSOLUTE
    ..\\Component\\motor\\Motor.c            0x00000000   Number         0  motor.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  memcpya.o(.text)
    .text                                    0x0800024a   Section        0  memseta.o(.text)
    .text                                    0x0800026e   Section        0  memcmp.o(.text)
    .text                                    0x08000288   Section        0  dadd.o(.text)
    .text                                    0x080003d6   Section        0  dmul.o(.text)
    .text                                    0x080004ba   Section        0  ddiv.o(.text)
    .text                                    0x08000598   Section        0  dflti.o(.text)
    .text                                    0x080005ba   Section        0  dfltui.o(.text)
    .text                                    0x080005d4   Section        0  f2d.o(.text)
    .text                                    0x080005fa   Section        0  d2f.o(.text)
    .text                                    0x08000632   Section        0  uidiv.o(.text)
    .text                                    0x0800065e   Section        0  llshl.o(.text)
    .text                                    0x0800067c   Section        0  llushr.o(.text)
    .text                                    0x0800069c   Section        0  llsshr.o(.text)
    .text                                    0x080006c0   Section        0  iusefp.o(.text)
    .text                                    0x080006c0   Section        0  fepilogue.o(.text)
    .text                                    0x0800072e   Section        0  depilogue.o(.text)
    .text                                    0x080007e8   Section        0  dfixul.o(.text)
    .text                                    0x08000818   Section       48  cdrcmple.o(.text)
    .text                                    0x08000848   Section       36  init.o(.text)
    .text                                    0x0800086c   Section        0  __dczerorl2.o(.text)
    i.Angle_PID_control                      0x080008c4   Section        0  control.o(i.Angle_PID_control)
    i.BusFault_Handler                       0x08000984   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream2_IRQHandler                0x08000988   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08000994   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08000995   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x080009bc   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080009bd   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08000a10   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000a11   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000a38   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Encoder_Driver_Init                    0x08000a3c   Section        0  encoder.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x08000a68   Section        0  encoder.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08000ad0   Section        0  encoder.o(i.Encoder_Init)
    i.Encoder_Task                           0x08000af8   Section        0  encoder.o(i.Encoder_Task)
    i.Error_Handler                          0x08000b10   Section        0  main.o(i.Error_Handler)
    i.Gray_Task                              0x08000b14   Section        0  track.o(i.Gray_Task)
    i.HAL_DMA_Abort                          0x08000b70   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000c02   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000c28   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000dc8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000e9c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000f0c   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000f30   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08001120   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800112a   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001134   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08001140   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Read                       0x080012c8   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_MspInit                        0x080014cc   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001538   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001548   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800157c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080015bc   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080015ec   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001608   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001648   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCCEx_PeriphCLKConfig              0x0800166c   Section        0  stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080017a8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080018dc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080018fc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800191c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800197c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001ce8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08001d10   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001d12   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001d14   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08001da4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001e00   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001ee0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08001f60   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x0800203c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x080020e0   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x080021a0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08002230   Section        0  tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IC_ConfigChannel               0x08002248   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    i.HAL_TIM_IC_Init                        0x0800236c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    i.HAL_TIM_IC_MspInit                     0x080023c6   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    i.HAL_TIM_IC_Start_IT                    0x080023c8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    i.HAL_TIM_IRQHandler                     0x080024d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08002604   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002658   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x0800265a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08002726   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002780   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08002782   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08002784   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x0800284c   Section        0  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_ReadCapturedValue              0x08002970   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    i.HAL_TIM_TriggerCallback                0x0800299a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x0800299c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080029e8   Section        0  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002a34   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002aa4   Section        0  usart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002abc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08002d3c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08002da0   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08002fa0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08002fbc   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08003010   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08003012   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080030b2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080030b4   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.Hcsr04Init                             0x080030b8   Section        0  hc_sr04.o(i.Hcsr04Init)
    i.Hcsr04TimIcIsr                         0x08003108   Section        0  hc_sr04.o(i.Hcsr04TimIcIsr)
    i.Hcsr04TimOverflowIsr                   0x08003254   Section        0  hc_sr04.o(i.Hcsr04TimOverflowIsr)
    i.I2C_IsAcknowledgeFailed                0x0800326c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x0800326d   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryRead                  0x0800329c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x0800329d   Thumb Code   246  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_WaitOnFlagUntilTimeout             0x08003398   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08003399   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08003428   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08003429   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x080034e4   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x080034e5   Thumb Code   112  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08003554   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08003555   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.IIC_Get_Digtal                         0x080035aa   Section        0  hardware_iic.o(i.IIC_Get_Digtal)
    i.IIC_ReadBytes                          0x080035c0   Section        0  hardware_iic.o(i.IIC_ReadBytes)
    i.JY901S_ClearDataReady                  0x080035e4   Section        0  jy901s_driver.o(i.JY901S_ClearDataReady)
    i.JY901S_Create                          0x080035f0   Section        0  jy901s_driver.o(i.JY901S_Create)
    i.JY901S_GetCalibrationProgress          0x08003632   Section        0  jy901s_driver.o(i.JY901S_GetCalibrationProgress)
    i.JY901S_GetData                         0x08003650   Section        0  jy901s_driver.o(i.JY901S_GetData)
    i.JY901S_Init                            0x08003658   Section        0  jy901s.o(i.JY901S_Init)
    i.JY901S_InitDataStructure               0x080036f0   Section        0  jy901s_driver.o(i.JY901S_InitDataStructure)
    JY901S_InitDataStructure                 0x080036f1   Thumb Code   118  jy901s_driver.o(i.JY901S_InitDataStructure)
    i.JY901S_IsCalibrated                    0x0800376c   Section        0  jy901s_driver.o(i.JY901S_IsCalibrated)
    i.JY901S_IsDataReady                     0x08003776   Section        0  jy901s_driver.o(i.JY901S_IsDataReady)
    i.JY901S_ParseByte                       0x08003780   Section        0  jy901s_driver.o(i.JY901S_ParseByte)
    i.JY901S_ProcessCalibration              0x08003848   Section        0  jy901s_driver.o(i.JY901S_ProcessCalibration)
    JY901S_ProcessCalibration                0x08003849   Thumb Code   248  jy901s_driver.o(i.JY901S_ProcessCalibration)
    i.JY901S_ProcessPacket                   0x08003944   Section        0  jy901s_driver.o(i.JY901S_ProcessPacket)
    JY901S_ProcessPacket                     0x08003945   Thumb Code   384  jy901s_driver.o(i.JY901S_ProcessPacket)
    i.JY901S_ResetParser                     0x08003ad4   Section        0  jy901s_driver.o(i.JY901S_ResetParser)
    JY901S_ResetParser                       0x08003ad5   Thumb Code    14  jy901s_driver.o(i.JY901S_ResetParser)
    i.JY901S_SetDataFilter                   0x08003ae2   Section        0  jy901s_driver.o(i.JY901S_SetDataFilter)
    i.JY901S_StartCalibration                0x08003aec   Section        0  jy901s_driver.o(i.JY901S_StartCalibration)
    i.JY901S_StartReceive                    0x08003b38   Section        0  jy901s_driver.o(i.JY901S_StartReceive)
    i.JY901S_Task                            0x08003b60   Section        0  jy901s.o(i.JY901S_Task)
    i.JY901S_UART_ErrorCallback              0x08003b90   Section        0  jy901s_driver.o(i.JY901S_UART_ErrorCallback)
    i.JY901S_UART_RxCallback                 0x08003bda   Section        0  jy901s_driver.o(i.JY901S_UART_RxCallback)
    i.Led_Display                            0x08003c04   Section        0  led.o(i.Led_Display)
    i.Led_Task                               0x08003c78   Section        0  led.o(i.Led_Task)
    i.Line_PID_control                       0x08003c84   Section        0  control.o(i.Line_PID_control)
    i.MX_DMA_Init                            0x08003cec   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08003d18   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C2_Init                           0x08003e4c   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM11_Init                          0x08003e8c   Section        0  tim.o(i.MX_TIM11_Init)
    i.MX_TIM1_Init                           0x08003eec   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08003f58   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08003fbc   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004028   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x080040d0   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_USART1_UART_Init                    0x08004108   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004140   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004178   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x080041b0   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x080041f4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Config_Init                      0x080041f6   Section        0  motor.o(i.Motor_Config_Init)
    i.Motor_Dead_Compensation                0x08004264   Section        0  motor.o(i.Motor_Dead_Compensation)
    i.Motor_Init                             0x08004284   Section        0  motor.o(i.Motor_Init)
    i.Motor_Limit_Speed                      0x080042d8   Section        0  motor.o(i.Motor_Limit_Speed)
    i.Motor_Set_Speed                        0x080042ea   Section        0  motor.o(i.Motor_Set_Speed)
    i.Motor_Stop                             0x0800438e   Section        0  motor.o(i.Motor_Stop)
    i.NMI_Handler                            0x080043d2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x080043d4   Section        0  control.o(i.PID_Init)
    i.PID_Task                               0x08004488   Section        0  control.o(i.PID_Task)
    i.PendSV_Handler                         0x08004590   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.PrintTrackerStatus                     0x08004594   Section        0  track.o(i.PrintTrackerStatus)
    i.SVC_Handler                            0x080045e8   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Scheduler_Init                         0x080045ec   Section        0  scheduler.o(i.Scheduler_Init)
    i.Scheduler_Run                          0x08004600   Section        0  scheduler.o(i.Scheduler_Run)
    i.SysTick_Handler                        0x08004640   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08004644   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080046d8   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x080046e8   Section        0  scheduler.o(i.System_Init)
    i.TIM1_TRG_COM_TIM11_IRQHandler          0x08004718   Section        0  stm32f4xx_it.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    i.TIM2_IRQHandler                        0x08004734   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM4_IRQHandler                        0x08004740   Section        0  stm32f4xx_it.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x0800474c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x0800481c   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08004836   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x0800484a   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x0800484b   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x0800485c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x0800485d   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080048bc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08004928   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08004929   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08004990   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08004991   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x080049e0   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x080049e1   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI1_SetConfig                      0x08004a04   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    i.TIM_TI2_ConfigInputStage               0x08004a84   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08004a85   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.TIM_TI2_SetConfig                      0x08004aa8   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    TIM_TI2_SetConfig                        0x08004aa9   Thumb Code    54  stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig)
    i.UART4_IRQHandler                       0x08004ae0   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART_DMAAbortOnError                   0x08004aec   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08004aed   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08004afa   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08004afb   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08004b44   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08004b45   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08004bca   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08004bcb   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08004be8   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08004be9   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08004c36   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08004c37   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08004c52   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08004c53   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08004d14   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08004d15   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08004e20   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08004ec0   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08004ef6   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08004ef7   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08004f68   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08004f74   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08004f80   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x08004f8c   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x08004f98   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08004f9c   Section        0  printfa.o(i.__0vsnprintf)
    i.__NVIC_SetPriority                     0x08004fd0   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004fd1   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08004ff0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08004ffe   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08005000   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08005010   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08005011   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08005194   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08005195   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08005848   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08005849   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800586c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800586d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x0800589a   Section        0  printfa.o(i._snputc)
    _snputc                                  0x0800589b   Thumb Code    22  printfa.o(i._snputc)
    i.beep_task                              0x080058b0   Section        0  beep_app.o(i.beep_task)
    i.bit_array_and                          0x080058d0   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x080058d1   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x080058f6   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x080058f7   Thumb Code    30  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08005914   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08005915   Thumb Code    12  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x08005920   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x08005921   Thumb Code    18  ebtn.o(i.bit_array_get)
    i.button_init                            0x08005934   Section        0  button_app.o(i.button_init)
    i.button_task                            0x08005958   Section        0  button_app.o(i.button_task)
    i.ebtn_init                              0x08005964   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x080059a4   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08005a08   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08005a09   Thumb Code    46  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x08005a38   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x08005a39   Thumb Code   172  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08005ae4   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.main                                   0x08005b98   Section        0  main.o(i.main)
    i.my_get_key_state                       0x08005be0   Section        0  button_app.o(i.my_get_key_state)
    i.my_handle_key_event                    0x08005c28   Section        0  button_app.o(i.my_handle_key_event)
    i.my_printf                              0x08005c68   Section        0  uart_app.o(i.my_printf)
    i.pid_calculate_incremental              0x08005c9a   Section        0  pid.o(i.pid_calculate_incremental)
    i.pid_constrain                          0x08005d14   Section        0  pid.o(i.pid_constrain)
    i.pid_init                               0x08005d34   Section        0  pid.o(i.pid_init)
    i.pid_out_limit                          0x08005d64   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x08005d65   Thumb Code    38  pid.o(i.pid_out_limit)
    i.pid_reset                              0x08005d8c   Section        0  pid.o(i.pid_reset)
    i.pid_set_target                         0x08005db4   Section        0  pid.o(i.pid_set_target)
    i.prv_process_btn                        0x08005dc8   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08005dc9   Thumb Code   328  ebtn.o(i.prv_process_btn)
    i.read_line_sensors                      0x08005f14   Section        0  track.o(i.read_line_sensors)
    i.rt_ringbuffer_data_len                 0x08005f28   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_put                      0x08005f58   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08005fca   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    i.task_switch                            0x08005fec   Section        0  scheduler.o(i.task_switch)
    .constdata                               0x0800613c   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800613c   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08006144   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08006154   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800615c   Section       32  track.o(.constdata)
    .constdata                               0x0800617c   Section       14  button_app.o(.constdata)
    .data                                    0x20000000   Section        2  usart.o(.data)
    .data                                    0x20000004   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        8  track.o(.data)
    .data                                    0x2000001c   Section       68  scheduler.o(.data)
    measure_timer5ms                         0x20000021   Data           1  scheduler.o(.data)
    scheduler_task                           0x20000030   Data          48  scheduler.o(.data)
    .data                                    0x20000060   Section      168  button_app.o(.data)
    .data                                    0x20000108   Section        5  led.o(.data)
    temp_old                                 0x20000108   Data           1  led.o(.data)
    .data                                    0x20000110   Section       96  control.o(.data)
    filtered_speed_left                      0x20000118   Data           4  control.o(.data)
    filtered_speed_right                     0x2000011c   Data           4  control.o(.data)
    .data                                    0x20000170   Section        1  beep_app.o(.data)
    .data                                    0x20000174   Section        4  jy901s.o(.data)
    .bss                                     0x20000178   Section       84  i2c.o(.bss)
    .bss                                     0x200001cc   Section      360  tim.o(.bss)
    .bss                                     0x20000334   Section      584  usart.o(.bss)
    .bss                                     0x2000057c   Section       40  hc_sr04.o(.bss)
    .bss                                     0x200005a4   Section      240  control.o(.bss)
    .bss                                     0x20000694   Section      172  jy901s.o(.bss)
    .bss                                     0x20000740   Section       40  ebtn.o(.bss)
    ebtn_default                             0x20000740   Data          40  ebtn.o(.bss)
    .bss                                     0x20000768   Section       40  encoder.o(.bss)
    .bss                                     0x20000790   Section       72  motor.o(.bss)
    .bss                                     0x200007d8   Section      140  uart_driver.o(.bss)
    STACK                                    0x20000868   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000227   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000227   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800024b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800024b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000259   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000259   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000259   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800025d   Thumb Code    18  memseta.o(.text)
    memcmp                                   0x0800026f   Thumb Code    26  memcmp.o(.text)
    __aeabi_dadd                             0x08000289   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080003cb   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080003d1   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080003d7   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080004bb   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x08000599   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x080005bb   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2d                              0x080005d5   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x080005fb   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000633   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000633   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800065f   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800065f   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800067d   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800067d   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800069d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800069d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x080006c1   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x080006c1   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080006d3   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800072f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800074d   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080007e9   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000819   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000849   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000849   Thumb Code     0  init.o(.text)
    __decompress                             0x0800086d   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x0800086d   Thumb Code    86  __dczerorl2.o(.text)
    Angle_PID_control                        0x080008c5   Thumb Code   172  control.o(i.Angle_PID_control)
    BusFault_Handler                         0x08000985   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream2_IRQHandler                  0x08000989   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000a39   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Encoder_Driver_Init                      0x08000a3d   Thumb Code    38  encoder.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x08000a69   Thumb Code    92  encoder.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08000ad1   Thumb Code    28  encoder.o(i.Encoder_Init)
    Encoder_Task                             0x08000af9   Thumb Code    20  encoder.o(i.Encoder_Task)
    Error_Handler                            0x08000b11   Thumb Code     4  main.o(i.Error_Handler)
    Gray_Task                                0x08000b15   Thumb Code    80  track.o(i.Gray_Task)
    HAL_DMA_Abort                            0x08000b71   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000c03   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000c29   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000dc9   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000e9d   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000f0d   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000f31   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001121   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800112b   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001135   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08001141   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Read                         0x080012c9   Thumb Code   502  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_MspInit                          0x080014cd   Thumb Code    94  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001539   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001549   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800157d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080015bd   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080015ed   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001609   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001649   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_PeriphCLKConfig                0x0800166d   Thumb Code   296  stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080017a9   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080018dd   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080018fd   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800191d   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800197d   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001ce9   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08001d11   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001d13   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001d15   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001da5   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001e01   Thumb Code   206  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001ee1   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08001f61   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x0800203d   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x080020e1   Thumb Code   172  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x080021a1   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08002231   Thumb Code    16  tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_ConfigChannel                 0x08002249   Thumb Code   292  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x0800236d   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x080023c7   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_IT                      0x080023c9   Thumb Code   240  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT)
    HAL_TIM_IRQHandler                       0x080024d5   Thumb Code   304  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08002605   Thumb Code    70  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08002659   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x0800265b   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08002727   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002781   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08002783   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08002785   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x0800284d   Thumb Code   264  scheduler.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_ReadCapturedValue                0x08002971   Thumb Code    42  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    HAL_TIM_TriggerCallback                  0x0800299b   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x0800299d   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080029e9   Thumb Code    60  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002a35   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002aa5   Thumb Code    16  usart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002abd   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002d3d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002da1   Thumb Code   468  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08002fa1   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002fbd   Thumb Code    60  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003011   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08003013   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080030b3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080030b5   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    Hcsr04Init                               0x080030b9   Thumb Code    74  hc_sr04.o(i.Hcsr04Init)
    Hcsr04TimIcIsr                           0x08003109   Thumb Code   294  hc_sr04.o(i.Hcsr04TimIcIsr)
    Hcsr04TimOverflowIsr                     0x08003255   Thumb Code    18  hc_sr04.o(i.Hcsr04TimOverflowIsr)
    IIC_Get_Digtal                           0x080035ab   Thumb Code    20  hardware_iic.o(i.IIC_Get_Digtal)
    IIC_ReadBytes                            0x080035c1   Thumb Code    32  hardware_iic.o(i.IIC_ReadBytes)
    JY901S_ClearDataReady                    0x080035e5   Thumb Code    12  jy901s_driver.o(i.JY901S_ClearDataReady)
    JY901S_Create                            0x080035f1   Thumb Code    66  jy901s_driver.o(i.JY901S_Create)
    JY901S_GetCalibrationProgress            0x08003633   Thumb Code    30  jy901s_driver.o(i.JY901S_GetCalibrationProgress)
    JY901S_GetData                           0x08003651   Thumb Code     8  jy901s_driver.o(i.JY901S_GetData)
    JY901S_Init                              0x08003659   Thumb Code    84  jy901s.o(i.JY901S_Init)
    JY901S_IsCalibrated                      0x0800376d   Thumb Code    10  jy901s_driver.o(i.JY901S_IsCalibrated)
    JY901S_IsDataReady                       0x08003777   Thumb Code    10  jy901s_driver.o(i.JY901S_IsDataReady)
    JY901S_ParseByte                         0x08003781   Thumb Code   198  jy901s_driver.o(i.JY901S_ParseByte)
    JY901S_SetDataFilter                     0x08003ae3   Thumb Code    10  jy901s_driver.o(i.JY901S_SetDataFilter)
    JY901S_StartCalibration                  0x08003aed   Thumb Code    72  jy901s_driver.o(i.JY901S_StartCalibration)
    JY901S_StartReceive                      0x08003b39   Thumb Code    40  jy901s_driver.o(i.JY901S_StartReceive)
    JY901S_Task                              0x08003b61   Thumb Code    40  jy901s.o(i.JY901S_Task)
    JY901S_UART_ErrorCallback                0x08003b91   Thumb Code    74  jy901s_driver.o(i.JY901S_UART_ErrorCallback)
    JY901S_UART_RxCallback                   0x08003bdb   Thumb Code    40  jy901s_driver.o(i.JY901S_UART_RxCallback)
    Led_Display                              0x08003c05   Thumb Code   106  led.o(i.Led_Display)
    Led_Task                                 0x08003c79   Thumb Code     6  led.o(i.Led_Task)
    Line_PID_control                         0x08003c85   Thumb Code    92  control.o(i.Line_PID_control)
    MX_DMA_Init                              0x08003ced   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08003d19   Thumb Code   286  gpio.o(i.MX_GPIO_Init)
    MX_I2C2_Init                             0x08003e4d   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM11_Init                            0x08003e8d   Thumb Code    86  tim.o(i.MX_TIM11_Init)
    MX_TIM1_Init                             0x08003eed   Thumb Code   100  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08003f59   Thumb Code    96  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08003fbd   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004029   Thumb Code   160  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x080040d1   Thumb Code    48  usart.o(i.MX_UART4_Init)
    MX_USART1_UART_Init                      0x08004109   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004141   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004179   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x080041b1   Thumb Code    54  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x080041f5   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Config_Init                        0x080041f7   Thumb Code   110  motor.o(i.Motor_Config_Init)
    Motor_Dead_Compensation                  0x08004265   Thumb Code    30  motor.o(i.Motor_Dead_Compensation)
    Motor_Init                               0x08004285   Thumb Code    72  motor.o(i.Motor_Init)
    Motor_Limit_Speed                        0x080042d9   Thumb Code    18  motor.o(i.Motor_Limit_Speed)
    Motor_Set_Speed                          0x080042eb   Thumb Code   164  motor.o(i.Motor_Set_Speed)
    Motor_Stop                               0x0800438f   Thumb Code    68  motor.o(i.Motor_Stop)
    NMI_Handler                              0x080043d3   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x080043d5   Thumb Code   168  control.o(i.PID_Init)
    PID_Task                                 0x08004489   Thumb Code   226  control.o(i.PID_Task)
    PendSV_Handler                           0x08004591   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    PrintTrackerStatus                       0x08004595   Thumb Code    38  track.o(i.PrintTrackerStatus)
    SVC_Handler                              0x080045e9   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Scheduler_Init                           0x080045ed   Thumb Code    14  scheduler.o(i.Scheduler_Init)
    Scheduler_Run                            0x08004601   Thumb Code    60  scheduler.o(i.Scheduler_Run)
    SysTick_Handler                          0x08004641   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08004645   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x080046d9   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x080046e9   Thumb Code    40  scheduler.o(i.System_Init)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08004719   Thumb Code    18  stm32f4xx_it.o(i.TIM1_TRG_COM_TIM11_IRQHandler)
    TIM2_IRQHandler                          0x08004735   Thumb Code     6  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM4_IRQHandler                          0x08004741   Thumb Code     6  stm32f4xx_it.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x0800474d   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x0800481d   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08004837   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x080048bd   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    TIM_TI1_SetConfig                        0x08004a05   Thumb Code   100  stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig)
    UART4_IRQHandler                         0x08004ae1   Thumb Code     6  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART_Start_Receive_DMA                   0x08004e21   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08004ec1   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08004f69   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08004f75   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08004f81   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x08004f8d   Thumb Code     6  stm32f4xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x08004f99   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08004f9d   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08004f9d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08004f9d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08004f9d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08004f9d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x08004ff1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08004fff   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08005001   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    beep_task                                0x080058b1   Thumb Code    24  beep_app.o(i.beep_task)
    button_init                              0x08005935   Thumb Code    24  button_app.o(i.button_init)
    button_task                              0x08005959   Thumb Code     8  button_app.o(i.button_task)
    ebtn_init                                0x08005965   Thumb Code    58  ebtn.o(i.ebtn_init)
    ebtn_process                             0x080059a5   Thumb Code    94  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08005ae5   Thumb Code   174  ebtn.o(i.ebtn_process_with_curr_state)
    main                                     0x08005b99   Thumb Code    70  main.o(i.main)
    my_get_key_state                         0x08005be1   Thumb Code    62  button_app.o(i.my_get_key_state)
    my_handle_key_event                      0x08005c29   Thumb Code    56  button_app.o(i.my_handle_key_event)
    my_printf                                0x08005c69   Thumb Code    50  uart_app.o(i.my_printf)
    pid_calculate_incremental                0x08005c9b   Thumb Code   122  pid.o(i.pid_calculate_incremental)
    pid_constrain                            0x08005d15   Thumb Code    32  pid.o(i.pid_constrain)
    pid_init                                 0x08005d35   Thumb Code    42  pid.o(i.pid_init)
    pid_reset                                0x08005d8d   Thumb Code    34  pid.o(i.pid_reset)
    pid_set_target                           0x08005db5   Thumb Code    20  pid.o(i.pid_set_target)
    read_line_sensors                        0x08005f15   Thumb Code    14  track.o(i.read_line_sensors)
    rt_ringbuffer_data_len                   0x08005f29   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_put                        0x08005f59   Thumb Code   114  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x08005fcb   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    task_switch                              0x08005fed   Thumb Code   266  scheduler.o(i.task_switch)
    AHBPrescTable                            0x08006144   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08006154   Data           8  system_stm32f4xx.o(.constdata)
    weights                                  0x0800615c   Data          32  track.o(.constdata)
    default_param_normal                     0x0800617c   Data          14  button_app.o(.constdata)
    Region$$Table$$Base                      0x0800618c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080061ac   Number         0  anon$$obj.o(Region$$Table)
    Serial_RxFlag                            0x20000000   Data           1  usart.o(.data)
    Serial_RxData                            0x20000001   Data           1  usart.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    Digital                                  0x20000014   Data           1  track.o(.data)
    g_line_position_error                    0x20000018   Data           4  track.o(.data)
    input_flag                               0x2000001c   Data           1  scheduler.o(.data)
    point_count                              0x2000001d   Data           1  scheduler.o(.data)
    output_flag                              0x2000001e   Data           1  scheduler.o(.data)
    mode_switch                              0x2000001f   Data           1  scheduler.o(.data)
    circle_count                             0x20000020   Data           1  scheduler.o(.data)
    task_num                                 0x20000022   Data           1  scheduler.o(.data)
    input_timer_500ms                        0x20000024   Data           2  scheduler.o(.data)
    output_timer_500ms                       0x20000026   Data           2  scheduler.o(.data)
    distance                                 0x20000028   Data           2  scheduler.o(.data)
    led_timer_500ms                          0x2000002a   Data           2  scheduler.o(.data)
    beep_timer_100ms                         0x2000002c   Data           2  scheduler.o(.data)
    static_buttons                           0x20000060   Data         168  button_app.o(.data)
    led_buf                                  0x20000109   Data           4  led.o(.data)
    pid_running                              0x20000110   Data           1  control.o(.data)
    pid_mode                                 0x20000111   Data           1  control.o(.data)
    basic_speed                              0x20000114   Data           4  control.o(.data)
    pid_params_speed_left                    0x20000120   Data          20  control.o(.data)
    pid_params_speed_right                   0x20000134   Data          20  control.o(.data)
    pid_params_line                          0x20000148   Data          20  control.o(.data)
    pid_params_angle                         0x2000015c   Data          20  control.o(.data)
    beep_status                              0x20000170   Data           1  beep_app.o(.data)
    yaw1                                     0x20000174   Data           4  jy901s.o(.data)
    hi2c2                                    0x20000178   Data          84  i2c.o(.bss)
    htim1                                    0x200001cc   Data          72  tim.o(.bss)
    htim2                                    0x20000214   Data          72  tim.o(.bss)
    htim3                                    0x2000025c   Data          72  tim.o(.bss)
    htim4                                    0x200002a4   Data          72  tim.o(.bss)
    htim11                                   0x200002ec   Data          72  tim.o(.bss)
    rx_buffer                                0x20000334   Data         128  usart.o(.bss)
    huart4                                   0x200003b4   Data          72  usart.o(.bss)
    huart1                                   0x200003fc   Data          72  usart.o(.bss)
    huart2                                   0x20000444   Data          72  usart.o(.bss)
    huart3                                   0x2000048c   Data          72  usart.o(.bss)
    huart6                                   0x200004d4   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x2000051c   Data          96  usart.o(.bss)
    Hcsr04Info                               0x2000057c   Data          40  hc_sr04.o(.bss)
    pid_speed_left                           0x200005a4   Data          60  control.o(.bss)
    pid_speed_right                          0x200005e0   Data          60  control.o(.bss)
    pid_line                                 0x2000061c   Data          60  control.o(.bss)
    pid_angle                                0x20000658   Data          60  control.o(.bss)
    jy901s_handle                            0x20000694   Data         172  jy901s.o(.bss)
    left_encoder                             0x20000768   Data          20  encoder.o(.bss)
    right_encoder                            0x2000077c   Data          20  encoder.o(.bss)
    left_motor                               0x20000790   Data          36  motor.o(.bss)
    right_motor                              0x200007b4   Data          36  motor.o(.bss)
    uart_rx_dma_buffer                       0x200007d8   Data         128  uart_driver.o(.bss)
    ring_buffer                              0x20000858   Data          12  uart_driver.o(.bss)
    __initial_sp                             0x20000c68   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006324, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0000621c])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000061ac, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         5090  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         5147    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         5150    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5152    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5154    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         5155    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         5162    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5157    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5159    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         5148    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         5093    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000024   Code   RO         5095    .text               mc_w.l(memcpya.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         5097    .text               mc_w.l(memseta.o)
    0x0800026e   0x0800026e   0x0000001a   Code   RO         5101    .text               mc_w.l(memcmp.o)
    0x08000288   0x08000288   0x0000014e   Code   RO         5133    .text               mf_w.l(dadd.o)
    0x080003d6   0x080003d6   0x000000e4   Code   RO         5135    .text               mf_w.l(dmul.o)
    0x080004ba   0x080004ba   0x000000de   Code   RO         5137    .text               mf_w.l(ddiv.o)
    0x08000598   0x08000598   0x00000022   Code   RO         5139    .text               mf_w.l(dflti.o)
    0x080005ba   0x080005ba   0x0000001a   Code   RO         5141    .text               mf_w.l(dfltui.o)
    0x080005d4   0x080005d4   0x00000026   Code   RO         5143    .text               mf_w.l(f2d.o)
    0x080005fa   0x080005fa   0x00000038   Code   RO         5145    .text               mf_w.l(d2f.o)
    0x08000632   0x08000632   0x0000002c   Code   RO         5164    .text               mc_w.l(uidiv.o)
    0x0800065e   0x0800065e   0x0000001e   Code   RO         5166    .text               mc_w.l(llshl.o)
    0x0800067c   0x0800067c   0x00000020   Code   RO         5168    .text               mc_w.l(llushr.o)
    0x0800069c   0x0800069c   0x00000024   Code   RO         5170    .text               mc_w.l(llsshr.o)
    0x080006c0   0x080006c0   0x00000000   Code   RO         5172    .text               mc_w.l(iusefp.o)
    0x080006c0   0x080006c0   0x0000006e   Code   RO         5173    .text               mf_w.l(fepilogue.o)
    0x0800072e   0x0800072e   0x000000ba   Code   RO         5175    .text               mf_w.l(depilogue.o)
    0x080007e8   0x080007e8   0x00000030   Code   RO         5177    .text               mf_w.l(dfixul.o)
    0x08000818   0x08000818   0x00000030   Code   RO         5179    .text               mf_w.l(cdrcmple.o)
    0x08000848   0x08000848   0x00000024   Code   RO         5181    .text               mc_w.l(init.o)
    0x0800086c   0x0800086c   0x00000056   Code   RO         5191    .text               mc_w.l(__dczerorl2.o)
    0x080008c2   0x080008c2   0x00000002   PAD
    0x080008c4   0x080008c4   0x000000c0   Code   RO         4095    i.Angle_PID_control  control.o
    0x08000984   0x08000984   0x00000002   Code   RO          554    i.BusFault_Handler  stm32f4xx_it.o
    0x08000986   0x08000986   0x00000002   PAD
    0x08000988   0x08000988   0x0000000c   Code   RO          555    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000994   0x08000994   0x00000028   Code   RO         1622    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x080009bc   0x080009bc   0x00000054   Code   RO         1623    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000a10   0x08000a10   0x00000028   Code   RO         1624    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08000a38   0x08000a38   0x00000002   Code   RO          556    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000a3a   0x08000a3a   0x00000002   PAD
    0x08000a3c   0x08000a3c   0x0000002c   Code   RO         4375    i.Encoder_Driver_Init  encoder.o
    0x08000a68   0x08000a68   0x00000068   Code   RO         4376    i.Encoder_Driver_Update  encoder.o
    0x08000ad0   0x08000ad0   0x00000028   Code   RO         4377    i.Encoder_Init      encoder.o
    0x08000af8   0x08000af8   0x00000018   Code   RO         4378    i.Encoder_Task      encoder.o
    0x08000b10   0x08000b10   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000b14   0x08000b14   0x0000005c   Code   RO         3864    i.Gray_Task         track.o
    0x08000b70   0x08000b70   0x00000092   Code   RO         1625    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08000c02   0x08000c02   0x00000024   Code   RO         1626    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000c26   0x08000c26   0x00000002   PAD
    0x08000c28   0x08000c28   0x000001a0   Code   RO         1630    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08000dc8   0x08000dc8   0x000000d4   Code   RO         1631    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08000e9c   0x08000e9c   0x0000006e   Code   RO         1635    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08000f0a   0x08000f0a   0x00000002   PAD
    0x08000f0c   0x08000f0c   0x00000024   Code   RO         2062    i.HAL_Delay         stm32f4xx_hal.o
    0x08000f30   0x08000f30   0x000001f0   Code   RO         1518    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001120   0x08001120   0x0000000a   Code   RO         1520    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800112a   0x0800112a   0x0000000a   Code   RO         1522    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001134   0x08001134   0x0000000c   Code   RO         2068    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001140   0x08001140   0x00000188   Code   RO          719    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080012c8   0x080012c8   0x00000204   Code   RO          737    i.HAL_I2C_Mem_Read  stm32f4xx_hal_i2c.o
    0x080014cc   0x080014cc   0x0000006c   Code   RO          311    i.HAL_I2C_MspInit   i2c.o
    0x08001538   0x08001538   0x00000010   Code   RO         2074    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001548   0x08001548   0x00000034   Code   RO         2075    i.HAL_Init          stm32f4xx_hal.o
    0x0800157c   0x0800157c   0x00000040   Code   RO         2076    i.HAL_InitTick      stm32f4xx_hal.o
    0x080015bc   0x080015bc   0x00000030   Code   RO          684    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x080015ec   0x080015ec   0x0000001a   Code   RO         1910    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001606   0x08001606   0x00000002   PAD
    0x08001608   0x08001608   0x00000040   Code   RO         1916    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001648   0x08001648   0x00000024   Code   RO         1917    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x0800166c   0x0800166c   0x0000013c   Code   RO         1269    i.HAL_RCCEx_PeriphCLKConfig  stm32f4xx_hal_rcc_ex.o
    0x080017a8   0x080017a8   0x00000134   Code   RO         1164    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080018dc   0x080018dc   0x00000020   Code   RO         1171    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x080018fc   0x080018fc   0x00000020   Code   RO         1172    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800191c   0x0800191c   0x00000060   Code   RO         1173    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800197c   0x0800197c   0x0000036c   Code   RO         1176    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001ce8   0x08001ce8   0x00000028   Code   RO         1921    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001d10   0x08001d10   0x00000002   Code   RO         3019    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08001d12   0x08001d12   0x00000002   Code   RO         3020    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08001d14   0x08001d14   0x00000090   Code   RO         3038    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08001da4   0x08001da4   0x0000005a   Code   RO         2315    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08001dfe   0x08001dfe   0x00000002   PAD
    0x08001e00   0x08001e00   0x000000e0   Code   RO          353    i.HAL_TIM_Base_MspInit  tim.o
    0x08001ee0   0x08001ee0   0x00000080   Code   RO         2320    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08001f60   0x08001f60   0x000000dc   Code   RO         2324    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x0800203c   0x0800203c   0x000000a4   Code   RO         2336    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x080020e0   0x080020e0   0x000000c0   Code   RO          355    i.HAL_TIM_Encoder_MspInit  tim.o
    0x080021a0   0x080021a0   0x0000008e   Code   RO         2339    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x0800222e   0x0800222e   0x00000002   PAD
    0x08002230   0x08002230   0x00000018   Code   RO          356    i.HAL_TIM_IC_CaptureCallback  tim.o
    0x08002248   0x08002248   0x00000124   Code   RO         2351    i.HAL_TIM_IC_ConfigChannel  stm32f4xx_hal_tim.o
    0x0800236c   0x0800236c   0x0000005a   Code   RO         2354    i.HAL_TIM_IC_Init   stm32f4xx_hal_tim.o
    0x080023c6   0x080023c6   0x00000002   Code   RO         2356    i.HAL_TIM_IC_MspInit  stm32f4xx_hal_tim.o
    0x080023c8   0x080023c8   0x0000010c   Code   RO         2359    i.HAL_TIM_IC_Start_IT  stm32f4xx_hal_tim.o
    0x080024d4   0x080024d4   0x00000130   Code   RO         2363    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08002604   0x08002604   0x00000054   Code   RO          357    i.HAL_TIM_MspPostInit  tim.o
    0x08002658   0x08002658   0x00000002   Code   RO         2366    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x0800265a   0x0800265a   0x000000cc   Code   RO         2387    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08002726   0x08002726   0x0000005a   Code   RO         2390    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08002780   0x08002780   0x00000002   Code   RO         2392    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08002782   0x08002782   0x00000002   Code   RO         2393    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08002784   0x08002784   0x000000c8   Code   RO         2395    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x0800284c   0x0800284c   0x00000124   Code   RO         3914    i.HAL_TIM_PeriodElapsedCallback  scheduler.o
    0x08002970   0x08002970   0x0000002a   Code   RO         2403    i.HAL_TIM_ReadCapturedValue  stm32f4xx_hal_tim.o
    0x0800299a   0x0800299a   0x00000002   Code   RO         2406    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x0800299c   0x0800299c   0x0000004a   Code   RO         3296    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x080029e6   0x080029e6   0x00000002   PAD
    0x080029e8   0x080029e8   0x0000004c   Code   RO         4870    i.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002a34   0x08002a34   0x00000070   Code   RO         3310    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002aa4   0x08002aa4   0x00000018   Code   RO          442    i.HAL_UART_ErrorCallback  usart.o
    0x08002abc   0x08002abc   0x00000280   Code   RO         3315    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002d3c   0x08002d3c   0x00000064   Code   RO         3316    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08002da0   0x08002da0   0x00000200   Code   RO          444    i.HAL_UART_MspInit  usart.o
    0x08002fa0   0x08002fa0   0x0000001c   Code   RO         3321    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08002fbc   0x08002fbc   0x00000054   Code   RO          445    i.HAL_UART_RxCpltCallback  usart.o
    0x08003010   0x08003010   0x00000002   Code   RO         3323    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08003012   0x08003012   0x000000a0   Code   RO         3324    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080030b2   0x080030b2   0x00000002   Code   RO         3327    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080030b4   0x080030b4   0x00000002   Code   RO          557    i.HardFault_Handler  stm32f4xx_it.o
    0x080030b6   0x080030b6   0x00000002   PAD
    0x080030b8   0x080030b8   0x00000050   Code   RO         3687    i.Hcsr04Init        hc_sr04.o
    0x08003108   0x08003108   0x0000014c   Code   RO         3690    i.Hcsr04TimIcIsr    hc_sr04.o
    0x08003254   0x08003254   0x00000018   Code   RO         3691    i.Hcsr04TimOverflowIsr  hc_sr04.o
    0x0800326c   0x0800326c   0x0000002e   Code   RO          762    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x0800329a   0x0800329a   0x00000002   PAD
    0x0800329c   0x0800329c   0x000000fc   Code   RO          772    i.I2C_RequestMemoryRead  stm32f4xx_hal_i2c.o
    0x08003398   0x08003398   0x00000090   Code   RO          778    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003428   0x08003428   0x000000bc   Code   RO          779    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080034e4   0x080034e4   0x00000070   Code   RO          780    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003554   0x08003554   0x00000056   Code   RO          781    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080035aa   0x080035aa   0x00000014   Code   RO         4425    i.IIC_Get_Digtal    hardware_iic.o
    0x080035be   0x080035be   0x00000002   PAD
    0x080035c0   0x080035c0   0x00000024   Code   RO         4429    i.IIC_ReadBytes     hardware_iic.o
    0x080035e4   0x080035e4   0x0000000c   Code   RO         4908    i.JY901S_ClearDataReady  jy901s_driver.o
    0x080035f0   0x080035f0   0x00000042   Code   RO         4909    i.JY901S_Create     jy901s_driver.o
    0x08003632   0x08003632   0x0000001e   Code   RO         4911    i.JY901S_GetCalibrationProgress  jy901s_driver.o
    0x08003650   0x08003650   0x00000008   Code   RO         4912    i.JY901S_GetData    jy901s_driver.o
    0x08003658   0x08003658   0x00000098   Code   RO         4185    i.JY901S_Init       jy901s.o
    0x080036f0   0x080036f0   0x0000007c   Code   RO         4918    i.JY901S_InitDataStructure  jy901s_driver.o
    0x0800376c   0x0800376c   0x0000000a   Code   RO         4919    i.JY901S_IsCalibrated  jy901s_driver.o
    0x08003776   0x08003776   0x0000000a   Code   RO         4920    i.JY901S_IsDataReady  jy901s_driver.o
    0x08003780   0x08003780   0x000000c6   Code   RO         4924    i.JY901S_ParseByte  jy901s_driver.o
    0x08003846   0x08003846   0x00000002   PAD
    0x08003848   0x08003848   0x000000fc   Code   RO         4925    i.JY901S_ProcessCalibration  jy901s_driver.o
    0x08003944   0x08003944   0x00000190   Code   RO         4926    i.JY901S_ProcessPacket  jy901s_driver.o
    0x08003ad4   0x08003ad4   0x0000000e   Code   RO         4928    i.JY901S_ResetParser  jy901s_driver.o
    0x08003ae2   0x08003ae2   0x0000000a   Code   RO         4932    i.JY901S_SetDataFilter  jy901s_driver.o
    0x08003aec   0x08003aec   0x0000004c   Code   RO         4933    i.JY901S_StartCalibration  jy901s_driver.o
    0x08003b38   0x08003b38   0x00000028   Code   RO         4934    i.JY901S_StartReceive  jy901s_driver.o
    0x08003b60   0x08003b60   0x00000030   Code   RO         4186    i.JY901S_Task       jy901s.o
    0x08003b90   0x08003b90   0x0000004a   Code   RO         4935    i.JY901S_UART_ErrorCallback  jy901s_driver.o
    0x08003bda   0x08003bda   0x00000028   Code   RO         4936    i.JY901S_UART_RxCallback  jy901s_driver.o
    0x08003c02   0x08003c02   0x00000002   PAD
    0x08003c04   0x08003c04   0x00000074   Code   RO         4015    i.Led_Display       led.o
    0x08003c78   0x08003c78   0x0000000c   Code   RO         4016    i.Led_Task          led.o
    0x08003c84   0x08003c84   0x00000068   Code   RO         4096    i.Line_PID_control  control.o
    0x08003cec   0x08003cec   0x0000002c   Code   RO          286    i.MX_DMA_Init       dma.o
    0x08003d18   0x08003d18   0x00000134   Code   RO          262    i.MX_GPIO_Init      gpio.o
    0x08003e4c   0x08003e4c   0x00000040   Code   RO          312    i.MX_I2C2_Init      i2c.o
    0x08003e8c   0x08003e8c   0x00000060   Code   RO          358    i.MX_TIM11_Init     tim.o
    0x08003eec   0x08003eec   0x0000006c   Code   RO          359    i.MX_TIM1_Init      tim.o
    0x08003f58   0x08003f58   0x00000064   Code   RO          360    i.MX_TIM2_Init      tim.o
    0x08003fbc   0x08003fbc   0x0000006c   Code   RO          361    i.MX_TIM3_Init      tim.o
    0x08004028   0x08004028   0x000000a8   Code   RO          362    i.MX_TIM4_Init      tim.o
    0x080040d0   0x080040d0   0x00000038   Code   RO          446    i.MX_UART4_Init     usart.o
    0x08004108   0x08004108   0x00000038   Code   RO          447    i.MX_USART1_UART_Init  usart.o
    0x08004140   0x08004140   0x00000038   Code   RO          448    i.MX_USART2_UART_Init  usart.o
    0x08004178   0x08004178   0x00000038   Code   RO          449    i.MX_USART3_UART_Init  usart.o
    0x080041b0   0x080041b0   0x00000044   Code   RO          450    i.MX_USART6_UART_Init  usart.o
    0x080041f4   0x080041f4   0x00000002   Code   RO          558    i.MemManage_Handler  stm32f4xx_it.o
    0x080041f6   0x080041f6   0x0000006e   Code   RO         4666    i.Motor_Config_Init  motor.o
    0x08004264   0x08004264   0x0000001e   Code   RO         4667    i.Motor_Dead_Compensation  motor.o
    0x08004282   0x08004282   0x00000002   PAD
    0x08004284   0x08004284   0x00000054   Code   RO         4668    i.Motor_Init        motor.o
    0x080042d8   0x080042d8   0x00000012   Code   RO         4669    i.Motor_Limit_Speed  motor.o
    0x080042ea   0x080042ea   0x000000a4   Code   RO         4670    i.Motor_Set_Speed   motor.o
    0x0800438e   0x0800438e   0x00000044   Code   RO         4671    i.Motor_Stop        motor.o
    0x080043d2   0x080043d2   0x00000002   Code   RO          559    i.NMI_Handler       stm32f4xx_it.o
    0x080043d4   0x080043d4   0x000000b4   Code   RO         4097    i.PID_Init          control.o
    0x08004488   0x08004488   0x00000108   Code   RO         4098    i.PID_Task          control.o
    0x08004590   0x08004590   0x00000002   Code   RO          560    i.PendSV_Handler    stm32f4xx_it.o
    0x08004592   0x08004592   0x00000002   PAD
    0x08004594   0x08004594   0x00000054   Code   RO         3865    i.PrintTrackerStatus  track.o
    0x080045e8   0x080045e8   0x00000002   Code   RO          561    i.SVC_Handler       stm32f4xx_it.o
    0x080045ea   0x080045ea   0x00000002   PAD
    0x080045ec   0x080045ec   0x00000014   Code   RO         3915    i.Scheduler_Init    scheduler.o
    0x08004600   0x08004600   0x00000040   Code   RO         3916    i.Scheduler_Run     scheduler.o
    0x08004640   0x08004640   0x00000004   Code   RO          562    i.SysTick_Handler   stm32f4xx_it.o
    0x08004644   0x08004644   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x080046d8   0x080046d8   0x00000010   Code   RO         3650    i.SystemInit        system_stm32f4xx.o
    0x080046e8   0x080046e8   0x00000030   Code   RO         3917    i.System_Init       scheduler.o
    0x08004718   0x08004718   0x0000001c   Code   RO          563    i.TIM1_TRG_COM_TIM11_IRQHandler  stm32f4xx_it.o
    0x08004734   0x08004734   0x0000000c   Code   RO          564    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08004740   0x08004740   0x0000000c   Code   RO          565    i.TIM4_IRQHandler   stm32f4xx_it.o
    0x0800474c   0x0800474c   0x000000d0   Code   RO         2408    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x0800481c   0x0800481c   0x0000001a   Code   RO         2409    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08004836   0x08004836   0x00000014   Code   RO         2419    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x0800484a   0x0800484a   0x00000010   Code   RO         2420    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x0800485a   0x0800485a   0x00000002   PAD
    0x0800485c   0x0800485c   0x00000060   Code   RO         2421    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080048bc   0x080048bc   0x0000006c   Code   RO         2422    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08004928   0x08004928   0x00000068   Code   RO         2423    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08004990   0x08004990   0x00000050   Code   RO         2424    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x080049e0   0x080049e0   0x00000022   Code   RO         2426    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08004a02   0x08004a02   0x00000002   PAD
    0x08004a04   0x08004a04   0x00000080   Code   RO         2427    i.TIM_TI1_SetConfig  stm32f4xx_hal_tim.o
    0x08004a84   0x08004a84   0x00000024   Code   RO         2428    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08004aa8   0x08004aa8   0x00000036   Code   RO         2429    i.TIM_TI2_SetConfig  stm32f4xx_hal_tim.o
    0x08004ade   0x08004ade   0x00000002   PAD
    0x08004ae0   0x08004ae0   0x0000000c   Code   RO          566    i.UART4_IRQHandler  stm32f4xx_it.o
    0x08004aec   0x08004aec   0x0000000e   Code   RO         3329    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08004afa   0x08004afa   0x0000004a   Code   RO         3330    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08004b44   0x08004b44   0x00000086   Code   RO         3331    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08004bca   0x08004bca   0x0000001e   Code   RO         3333    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08004be8   0x08004be8   0x0000004e   Code   RO         3339    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08004c36   0x08004c36   0x0000001c   Code   RO         3340    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08004c52   0x08004c52   0x000000c2   Code   RO         3341    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08004d14   0x08004d14   0x0000010c   Code   RO         3342    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08004e20   0x08004e20   0x000000a0   Code   RO         3343    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08004ec0   0x08004ec0   0x00000036   Code   RO         3344    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08004ef6   0x08004ef6   0x00000072   Code   RO         3345    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08004f68   0x08004f68   0x0000000c   Code   RO          567    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08004f74   0x08004f74   0x0000000c   Code   RO          568    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08004f80   0x08004f80   0x0000000c   Code   RO          569    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08004f8c   0x08004f8c   0x0000000c   Code   RO          570    i.USART6_IRQHandler  stm32f4xx_it.o
    0x08004f98   0x08004f98   0x00000002   Code   RO          571    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004f9a   0x08004f9a   0x00000002   PAD
    0x08004f9c   0x08004f9c   0x00000034   Code   RO         5111    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08004fd0   0x08004fd0   0x00000020   Code   RO         1923    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08004ff0   0x08004ff0   0x0000000e   Code   RO         5185    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08004ffe   0x08004ffe   0x00000002   Code   RO         5186    i.__scatterload_null  mc_w.l(handlers.o)
    0x08005000   0x08005000   0x0000000e   Code   RO         5187    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800500e   0x0800500e   0x00000002   PAD
    0x08005010   0x08005010   0x00000184   Code   RO         5113    i._fp_digits        mc_w.l(printfa.o)
    0x08005194   0x08005194   0x000006b4   Code   RO         5114    i._printf_core      mc_w.l(printfa.o)
    0x08005848   0x08005848   0x00000024   Code   RO         5115    i._printf_post_padding  mc_w.l(printfa.o)
    0x0800586c   0x0800586c   0x0000002e   Code   RO         5116    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800589a   0x0800589a   0x00000016   Code   RO         5117    i._snputc           mc_w.l(printfa.o)
    0x080058b0   0x080058b0   0x00000020   Code   RO         4151    i.beep_task         beep_app.o
    0x080058d0   0x080058d0   0x00000026   Code   RO         4221    i.bit_array_and     ebtn.o
    0x080058f6   0x080058f6   0x0000001e   Code   RO         4222    i.bit_array_assign  ebtn.o
    0x08005914   0x08005914   0x0000000c   Code   RO         4223    i.bit_array_cmp     ebtn.o
    0x08005920   0x08005920   0x00000012   Code   RO         4224    i.bit_array_get     ebtn.o
    0x08005932   0x08005932   0x00000002   PAD
    0x08005934   0x08005934   0x00000024   Code   RO         3969    i.button_init       button_app.o
    0x08005958   0x08005958   0x0000000c   Code   RO         3970    i.button_task       button_app.o
    0x08005964   0x08005964   0x00000040   Code   RO         4235    i.ebtn_init         ebtn.o
    0x080059a4   0x080059a4   0x00000064   Code   RO         4239    i.ebtn_process      ebtn.o
    0x08005a08   0x08005a08   0x0000002e   Code   RO         4240    i.ebtn_process_btn  ebtn.o
    0x08005a36   0x08005a36   0x00000002   PAD
    0x08005a38   0x08005a38   0x000000ac   Code   RO         4241    i.ebtn_process_btn_combo  ebtn.o
    0x08005ae4   0x08005ae4   0x000000b4   Code   RO         4242    i.ebtn_process_with_curr_state  ebtn.o
    0x08005b98   0x08005b98   0x00000046   Code   RO           15    i.main              main.o
    0x08005bde   0x08005bde   0x00000002   PAD
    0x08005be0   0x08005be0   0x00000048   Code   RO         3971    i.my_get_key_state  button_app.o
    0x08005c28   0x08005c28   0x00000040   Code   RO         3972    i.my_handle_key_event  button_app.o
    0x08005c68   0x08005c68   0x00000032   Code   RO         4054    i.my_printf         uart_app.o
    0x08005c9a   0x08005c9a   0x0000007a   Code   RO         4724    i.pid_calculate_incremental  pid.o
    0x08005d14   0x08005d14   0x00000020   Code   RO         4726    i.pid_constrain     pid.o
    0x08005d34   0x08005d34   0x00000030   Code   RO         4727    i.pid_init          pid.o
    0x08005d64   0x08005d64   0x00000026   Code   RO         4728    i.pid_out_limit     pid.o
    0x08005d8a   0x08005d8a   0x00000002   PAD
    0x08005d8c   0x08005d8c   0x00000028   Code   RO         4729    i.pid_reset         pid.o
    0x08005db4   0x08005db4   0x00000014   Code   RO         4732    i.pid_set_target    pid.o
    0x08005dc8   0x08005dc8   0x0000014c   Code   RO         4244    i.prv_process_btn   ebtn.o
    0x08005f14   0x08005f14   0x00000014   Code   RO         3866    i.read_line_sensors  track.o
    0x08005f28   0x08005f28   0x00000030   Code   RO         4791    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08005f58   0x08005f58   0x00000072   Code   RO         4796    i.rt_ringbuffer_put  ringbuffer.o
    0x08005fca   0x08005fca   0x00000020   Code   RO         4801    i.rt_ringbuffer_status  ringbuffer.o
    0x08005fea   0x08005fea   0x00000002   PAD
    0x08005fec   0x08005fec   0x00000150   Code   RO         3918    i.task_switch       scheduler.o
    0x0800613c   0x0800613c   0x00000008   Data   RO         1637    .constdata          stm32f4xx_hal_dma.o
    0x08006144   0x08006144   0x00000010   Data   RO         3651    .constdata          system_stm32f4xx.o
    0x08006154   0x08006154   0x00000008   Data   RO         3652    .constdata          system_stm32f4xx.o
    0x0800615c   0x0800615c   0x00000020   Data   RO         3867    .constdata          track.o
    0x0800617c   0x0800617c   0x0000000e   Data   RO         3973    .constdata          button_app.o
    0x0800618a   0x0800618a   0x00000002   PAD
    0x0800618c   0x0800618c   0x00000020   Data   RO         5183    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080061ac, Size: 0x00000c68, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x00000070])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000002   Data   RW          456    .data               usart.o
    0x20000002   COMPRESSED   0x00000002   PAD
    0x20000004   COMPRESSED   0x0000000c   Data   RW         2082    .data               stm32f4xx_hal.o
    0x20000010   COMPRESSED   0x00000004   Data   RW         3653    .data               system_stm32f4xx.o
    0x20000014   COMPRESSED   0x00000008   Data   RW         3868    .data               track.o
    0x2000001c   COMPRESSED   0x00000044   Data   RW         3919    .data               scheduler.o
    0x20000060   COMPRESSED   0x000000a8   Data   RW         3974    .data               button_app.o
    0x20000108   COMPRESSED   0x00000005   Data   RW         4017    .data               led.o
    0x2000010d   COMPRESSED   0x00000003   PAD
    0x20000110   COMPRESSED   0x00000060   Data   RW         4101    .data               control.o
    0x20000170   COMPRESSED   0x00000001   Data   RW         4152    .data               beep_app.o
    0x20000171   COMPRESSED   0x00000003   PAD
    0x20000174   COMPRESSED   0x00000004   Data   RW         4189    .data               jy901s.o
    0x20000178        -       0x00000054   Zero   RW          313    .bss                i2c.o
    0x200001cc        -       0x00000168   Zero   RW          363    .bss                tim.o
    0x20000334        -       0x00000248   Zero   RW          455    .bss                usart.o
    0x2000057c        -       0x00000028   Zero   RW         3693    .bss                hc_sr04.o
    0x200005a4        -       0x000000f0   Zero   RW         4100    .bss                control.o
    0x20000694        -       0x000000ac   Zero   RW         4187    .bss                jy901s.o
    0x20000740        -       0x00000028   Zero   RW         4245    .bss                ebtn.o
    0x20000768        -       0x00000028   Zero   RW         4379    .bss                encoder.o
    0x20000790        -       0x00000048   Zero   RW         4672    .bss                motor.o
    0x200007d8        -       0x0000008c   Zero   RW         4872    .bss                uart_driver.o
    0x20000864   COMPRESSED   0x00000004   PAD
    0x20000868        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800621c, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        32          8          0          1          0        718   beep_app.o
       184         42         14        168          0       2947   button_app.o
       740         82          0         96        240       3244   control.o
        44          4          0          0          0        818   dma.o
       992         22          0          0         40      16469   ebtn.o
       212         34          0          0         40       3581   encoder.o
       308         22          0          0          0       1131   gpio.o
        56          4          0          0          0       1872   hardware_iic.o
       436         50          0          0         40       3451   hc_sr04.o
       172         28          0          0         84       1757   i2c.o
       200         76          0          4        172       1764   jy901s.o
      1364         30          0          0          0      12345   jy901s_driver.o
       128         16          0          5          0       1627   led.o
       222         10          0          0          0     712657   main.o
       474         12          0          0         72       5259   motor.o
       300         12          0          0          0       4732   pid.o
       194          0          0          0          0       3637   ringbuffer.o
       760        116          0         68          0       4343   scheduler.o
        36          8        392          0       1024        856   startup_stm32f407xx.o
       180         28          0         12          0       9581   stm32f4xx_hal.o
       198         14          0          0          0      33943   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7538   stm32f4xx_hal_dma.o
       516         46          0          0          0       2931   stm32f4xx_hal_gpio.o
      1736         36          0          0          0      10270   stm32f4xx_hal_i2c.o
        48          6          0          0          0        902   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5380   stm32f4xx_hal_rcc.o
       316         20          0          0          0       1432   stm32f4xx_hal_rcc_ex.o
      3154        192          0          0          0      24980   stm32f4xx_hal_tim.o
       148         28          0          0          0       2569   stm32f4xx_hal_tim_ex.o
      2266         28          0          0          0      16491   stm32f4xx_hal_uart.o
       144         58          0          0          0       8827   stm32f4xx_it.o
        16          4         24          4          0       1187   system_stm32f4xx.o
      1104        100          0          0        360       6845   tim.o
       196         64         32          8          0       2429   track.o
        50          0          0          0          0       1382   uart_app.o
        76         16          0          0        140       1441   uart_driver.o
       912        122          0          2        584       8047   usart.o
         0          0          0          0          0       1232   wit_c_sdk.o

    ----------------------------------------------------------------------
     20392       <USER>        <GROUP>        376       2800     930615   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        50          0          2          8          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2260         86          0          0          0        528   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      4108        <USER>          <GROUP>          0          0       2360   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2774        102          0          0          0       1228   mc_w.l
      1330          0          0          0          0       1132   mf_w.l

    ----------------------------------------------------------------------
      4108        <USER>          <GROUP>          0          0       2360   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24500       1528        504        376       2800     913163   Grand Totals
     24500       1528        504        112       2800     913163   ELF Image Totals (compressed)
     24500       1528        504        112          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25004 (  24.42kB)
    Total RW  Size (RW Data + ZI Data)              3176 (   3.10kB)
    Total ROM Size (Code + RO Data + RW Data)      25116 (  24.53kB)

==============================================================================

